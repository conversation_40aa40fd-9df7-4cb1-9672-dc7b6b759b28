/*.txt
!robots.txt
*.log
/lib/logs
/resetpermissions.sh
temp/*
**/*.log
/captcha
.devilbox/*

# JetBrains
.idea/
*.ipr
*.iml
*.iws
*.idx
*.vscode
# node
node_modules/

.nyc_output/

.DS_Store
kubernetes.yaml

*.env*


#ASDF ignore
.tool-versions

#ignore local npm cache
/package-lock.json

#Mac
.DS_Store

.vscode/settings.json
.vscode/launch.json
.nasir
#stop master.vars
master.vars
/.zshrc
/docker-compose.override.yml
.github/instructions/agent.instructions.md
/.au*
.github/copilot-instructions.md
