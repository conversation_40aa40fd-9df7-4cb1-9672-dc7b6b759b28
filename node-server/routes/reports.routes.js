const express = require('express');
const reportsController = require('../controllers/reports.controller');
const controller = require("../controllers/pricing.controllers");
const router = express.Router();

router.get('/getFormToTable',reportsController.getFormToTable);
router.get('/service-range-data', reportsController.getServiceRangeData);
router.get('/material-range-data', reportsController.getMaterialRangeData);
router.post('/material-range-data', reportsController.getMaterialRangeData);
router.get('/getFormsByType',reportsController.getFormsByType);

router.get('/get-form-report-extended', reportsController.getFormReportExtended);
router.get('/chase-dashboard', reportsController.getChaseDashboard);
router.get('/get-per-occurance-job-cost-report', reportsController.getPerOccuranceJobCostReport);
router.get('/service-monitoring-request', reportsController.getServiceMonitoringRequest);
router.get('/get-services-snow-report', reportsController.getServicesSnowReport)
router.get('/snow-command-center-report', reportsController.getSnowCommandCenterReport)
router.post('/get-pricing', reportsController.getPricingReport);
router.get('/site-sessions-services', reportsController.siteSessionsServices);

router.post('/get-pricing-contractor', reportsController.getPricingReportContractor);

router.post('/get-pricing-client', reportsController.getPricingReportClient);


router.post('/get-contractor-details', reportsController.getContractorDetails);

router.get('/get-client-contract-types-by-sites', reportsController.getClientContractTypesBySites)

router.get('/checkins-without-checkout', reportsController.checkinsWithoutCheckout)

router.post('/send-batch-report-email', reportsController.sendBatchReportInEmail)

router.post('/get-report-email', reportsController.getReportEmail);
router.post('/update-report-email', reportsController.updateReportEmail);

router.post('/get-service-details', reportsController.getServiceDetails)

router.get('/report-all-contractors', reportsController.reportAllContractors);
router.get('/site-sessions', reportsController.siteSessions);
router.get('/time-sheet-weekly', reportsController.getWeeklyTimeSheet);
router.get('/sum-test', reportsController.getSumTestReport);
router.get('/service-tracker-site', reportsController.getServiceTrackerBySites);
router.post('/service-tracker-site-client', reportsController.getServiceTrackerBySitesClient);
router.post('/winter-service-tracker-wells-fargo', reportsController.getWinterServiceTrackerWellsFargo);
router.post('/weekly-landscaping-report-wells-fargo', reportsController.getWellsFargoWeeklyLandscapeReport);
router.post('/wells-fargo-snow-report', reportsController.getWellsFargoSnowStormReport);
router.post('/wells-fargo-snow-report2', reportsController.getWellsFargoSnowStormReport2);
router.get('/post-dated-services', reportsController.postDatedServices);
router.get('/truenorth-report1', reportsController.getTrueNorthReport1);
router.get('/snow-storms', reportsController.getSnowStorms);
router.get('/snow-storms-time-range-provider', reportsController.getStormsInTimeRangeProvider);
router.get('/snow-storms-time-range', reportsController.getStormsInTimeRange);
router.post('/form-to-table-to-google-sheet', reportsController.postFormToTableToGoogleSheets);
router.get('/sites-with-no-activity', reportsController.getSitesWithNoActivity);
router.post('/workorder-verification-report', reportsController.workOrderVerificationReport);
router.post('/export-report-to-google-sheet', reportsController.exportReportAsGoogleSheet);
router.post('/photo-report-local-time', reportsController.photoReportLocalTime);
router.get('/wells-fargo-pie-report', reportsController.getWellsFargoPieReport);
router.get('/wells-fargo-pie-report-rollup', reportsController.getWellsFargoPieReportRollup);

router.post('/wells-fargo-snow-storm-chart-report', reportsController.getWellsFargoSnowStormChartReport);
router.get('/get-cameras', reportsController.getCameras);

router.get('/issues-report', reportsController.getIssuesReport);

router.get('/verizon-landscape-chart-data', reportsController.getVerizonLandscapingData);
router.get('/verizon-spring-cleanup-chart-data', reportsController.getVerizonSpringCleanupData);
router.get('/verizon-janitorial-chart-data', reportsController.getVerizonJanitorialData);
router.get('/verizon-hvac-chart-data', reportsController.getVerizonHVACData);
router.get('/wells-fargo-dpr-events', reportsController.getWellsFargoDPREvents);
router.get('/wells-fargo-dpr-event/:event_id', reportsController.getWellsFargoDPREventData);
router.get('/wells-fargo-winter-services-table', reportsController.getWellsFargoWinterServicesTable);
router.get('/verizon-janatorial-chart-data-time', reportsController.getVerizonJanitorialChartDataTime);
router.get('/verizon-landscape-chart-data2', reportsController.getVerizonLandscapeChartData);
router.get('/verizon-landscape-tabulated-data', reportsController.getVerizonTabulatedLandScapeData)
router.get('/verizon-janitorial-tabulated-data', reportsController.getVerizonTabulatedJanitorialData)
router.get('/form2table', reportsController.form2table);
router.get('/usps-sites-report', reportsController.uspsSitesReport);
router.get('/safebysix', reportsController.safeBySix);
router.post('/exportsafesixreporttogsheet', reportsController.exportSafeBySixToGoogleSheet);

module.exports = router
