const service = require('../services/form2table.service')
const reportService = require('../services/reports.services')
const servicesService = require('../services/service.services')
const mysql = require('../utils/db')
const postgres = require('../utils/fleetdb')
const { login, getContractors } = require("../utils/vendor");
const JSONPath = require('jsonpath');
const zlib = require('zlib');
const { promisify } = require('util');
const gunzip = promisify(zlib.gunzip);
const Hashids = require('hashids/cjs')
const hashids = new Hashids('1a3371ee-68b4-47dc-9786-3d57d264240f')
const moment = require('moment-timezone');
const { sendEmailTemplate } = require('../utils/email');
const { awaitSafeQuery,awaitQuery } = require("../utils/db");
const baseURL = process.env.BASE_URL || 'http://localhost'
const { find } = require('geo-tz')
const { redisClient } = require('../utils/redis-client.js')
const uuid = require('uuid');

const { getSnowVendorMapping,getLandscapeVendorMapping } = require('../services/wellsfargo.services');

const getChaseDashboard = async (req, res, next) => {
  try {
    const { vendorId, internalUid, isHybrid, isClientViewEnabled } = await login(req.cookies.JWT);
    if (!vendorId) {
      return res.sendStatus(401);
    }
    return res.json(await reportService.getChaseDashboard(internalUid, vendorId));
  } catch (error) {
    next(error);
  }
}
const calculateEventStartEndTime = async (event, vendorId) => {

  const [eventData] = await mysql.awaitQuery(`SELECT * FROM sitefotos_user_field_events WHERE sufe_id = ? AND sufe_vendor_id = ?`, [event, vendorId]);
  const start = eventData.sufe_event_start_time;
  let end = null;

  if (eventData.sufe_event_end_time) {
    end = eventData.sufe_event_end_time;
  } else {
    const [nextEvent] = await mysql.awaitQuery(`
      SELECT sufe_event_start_time 
      FROM sitefotos_user_field_events 
      WHERE sufe_event_start_time > ? AND sufe_vendor_id = ?
      ORDER BY sufe_event_start_time ASC 
      LIMIT 1
    `, [start, vendorId]);

    if (nextEvent) {
      end = nextEvent.sufe_event_start_time - 1;
    } else {
      //if no end time it should be now in unixtime
      end = Math.floor(new Date().getTime() / 1000);
    }
  }

  return { start, end };
}
const getServiceMonitoringRequest = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId || vendorId != 17758) {
      return res.sendStatus(401);
    }

    let { event } = req.query;
    let eventName = '';
    if(!event || isNaN(event)) {
      const [latestEvent] = await awaitSafeQuery(`SELECT sufe_id as id, sufe_event_name as name FROM sitefotos_user_field_events WHERE sufe_vendor_id = ? ORDER BY sufe_event_start_time DESC LIMIT 1`, [vendorId]);
      event = latestEvent.id;
      eventName = latestEvent.name;
    }
    else {
      const [eventData] = await awaitSafeQuery(`SELECT sufe_event_name as name FROM sitefotos_user_field_events WHERE sufe_id = ?`, [event]);
      eventName = eventData.name;
    }
    const {start, end} = await calculateEventStartEndTime(event, vendorId);
    console.log(event, eventName, start, end);
    //Lets gather the site id's of CPS
    const sites = await mysql.awaitQuery(`select sscm_site_id,scs_client_internal_id, sscm_vendor_id,sscm_client_site_id, mb_address1, scs_client_site_name from sitefotos_site_client_mapping_extended_ungrouped left join maptile_building on sscm_site_id=mb_id where scs_client_vendor_id = 17758`);
    //Now lets gather the form submissions for those sites
    const forms = await mysql.awaitQuery(/*SQL*/`SELECT sfs.sfs_id, sfs.sfs_form_id, sfs.sfs_vendor_id, sf.sf_form_workticket_fiwo_form_template_id, sfs.sfs_building_id, sfs.sfs_created FROM sitefotos_forms_submitted sfs left join sitefotos_forms sf on sfs.sfs_wo_orignal_form_id = sf.sf_id WHERE sfs.sfs_building_id IN (?) AND sfs.sfs_created >= ? AND sfs.sfs_created <= ? AND sfs.sfs_vendor_id IN (19332,15108,20726); `, [sites.map(site => site.sscm_site_id), start, end]);

    //gather sites metadata for each unique sscm_client_site_id
    const uniqueClientSiteIds = [...new Set(sites.map(site => site.sscm_client_site_id))];
    const sitesMetadata = await mysql.awaitQuery(`select scsd_zone,scsd_property_manager,scsd_senior_property_manager,scs_id,scsd_location_type from sitefotos_client_sites left join sitefotos_client_sites_data on scsd_client_site_id=scs_id where scs_id in (?)`, [uniqueClientSiteIds]);
    const eventSiteData = await mysql.awaitQuery(`select * from sitefotos_user_field_events_site_data left join sitefotos_service_ticket_requests on sufesd_id = sstr_event_id where sufesd_event_id = ?`, [event]);
    const noParkingLotSites  = ["CPS - 51441","CPS - 51468","CPS - 51509","CPS - 52570","CPS - 59885","CPS - 54784","CPS - 51193","CPS - 51085","CPS - 51197","CPS - 51216","CPS - 51209","CPS - 51252","CPS - 56155","CPS - 51345","CPS - 52500","CPS - 51607","CPS - 51440","CPS - 51356","CPS - 51385","CPS - 51391","CPS - 59991","CPS - 51400","CPS - 51357","CPS - 50444","CPS - 51438","CPS - 51455","CPS - 59943","CPS - 51224","CPS - 50125","CPS - 51195","CPS - 51527","CPS - 51100","CPS - 58654","CPS - 51416","CPS - 51911"]

    const reportData = [];
    for(let site of sitesMetadata){
      //get mb_ids of all the sites that have the same scsd_client_site_id
      let mbIds = sites.filter(mySite => mySite.sscm_client_site_id == site.scs_id).map(mySite => mySite.sscm_site_id);

      let formSubmissions = forms.filter(form => mbIds.includes(parseInt(form.sfs_building_id)));

      let sitePointer = sites.find(mySite => mySite.sscm_client_site_id === site.scs_id);
      let eventSiteDataPointer = eventSiteData.filter(myEventSiteData => myEventSiteData.sufesd_site_id === site.scs_id);
      let data = {
        "Event ID": event,
        "Site ID": site.scs_id,
        "Facility ID": sitePointer.scs_client_internal_id,
        "Network": site.scsd_location_type,
        "Facility Name": sitePointer.scs_client_site_name,
        "Facility Address": sitePointer.mb_address1,
        "Quality Zone": site.scsd_zone,
        "Parking Lot Vendor": noParkingLotSites.includes(sitePointer.scs_client_internal_id) ? "N/A" : "Diaz Group",
        "Parking Lot Vendor Email": noParkingLotSites.includes(sitePointer.scs_client_internal_id) ? "" : "<EMAIL>",
        "Parking Lot Vendor Phone": noParkingLotSites.includes(sitePointer.scs_client_internal_id) ? "" : "(*************",
        "Sidewalk Vendor": "Outworx",
        "Sidewalk Vendor Email": "<EMAIL>",
        "Sidewalk Vendor Phone": "(*************",

        "Parking Lot Completion": formSubmissions.filter(form => form.sfs_form_id == 1409618),
        "Parking Lot Completion Date": formSubmissions.filter(form => form.sfs_form_id == 1409618).map(form => form.sfs_created),
        "Parking Lot Field Reports": formSubmissions.filter(form => form.sfs_form_id == 1421415),
        "Parking Lot BM Verification": eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot") ? eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot").sufesd_status : "",
        "Parking Lot BM Verification Date": eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot") ? eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot").sufesd_created_at : "",
        "Parking Lot BM Verification Notes": eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot") ? eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot").sufesd_notes : "",
        "Parking Lot Vendor Resolution": eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot") ? eventSiteDataPointer.find(event => event.sufesd_type === "parkinglot").sstr_status   : "",

        "Sidewalks Completion": formSubmissions.filter(form => form.sf_form_workticket_fiwo_form_template_id == 1426082),
        "Sidewalks Completion Date": formSubmissions.filter(form => form.sf_form_workticket_fiwo_form_template_id == 1426082).map(form => form.sfs_created),
        "Sidewalks Field Reports": formSubmissions.filter(form => form.sfs_form_id == 1421414),
        "Sidewalk BM Verification": eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk") ? eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk").sufesd_status : "",
        "Sidewalk BM Verification Date": eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk") ? eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk").sufesd_created_at : "",
        "Sidewalk BM Verification Notes": eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk") ? eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk").sufesd_notes : "",
        "Sidewalk Vendor Resolution": eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk") ? eventSiteDataPointer.find(event => event.sufesd_type === "sidewalk").sstr_status   : "",

        "Walkways BM Verification": eventSiteDataPointer.find(event => event.sufesd_type === "walkways") ? eventSiteDataPointer.find(event => event.sufesd_type === "walkways").sufesd_status : "",



        "BM EMAIL": site.scsd_property_manager,
        "AFM EMAIL": site.scsd_senior_property_manager
      }
      reportData.push(data);
    }
 
    res.json({reportData, event, eventName});

  } catch (error) {
    next(error);
  }
};

const getSumTestReport = async (req, res, next) => {
  try {
    const vendorId = login(req.cookies.JWT);
    if (!vendorId) {
      return res.sendStatus(401);
    }
    const { sDate, eDate } = req.query;
    let forms = await mysql.awaitQuery(`select * from sitefotos_forms_submitted left join maptile_building on sfs_building_id=mb_id left join sitefotos_forms on sfs_form_id = sf_id where sfs_form_id in (1201202,1108754) and sfs_created between ? and ?`, [sDate, eDate]);
    let data = [];
    for (let form of forms) {
      const jsonData = JSON.parse(form.sfs_form_data_full);
      let sum = 0;

      jsonData.pages.forEach(page => {
        page.elements.forEach(element => {
          if (element.type === "radiogroup" && !isNaN(element.value) && element.value !== "") {
            sum += parseInt(element.value, 10);
          }
        });
      });
      let object = {
        "Site": form.mb_nickname,
        "Form": form.sf_form_name,
        "Date": form.sfs_created,
        "Sum": sum
      };
      data.push(object);
    }
    res.json(data);

  } catch (error) {
    next(error);
  }
};
const photoReportLocalTime = async (req, res, next) => {
  try {
    let { photoIds } = req.body;


    if (typeof photoIds === 'string') {
      photoIds = JSON.parse(photoIds);
    }
    let { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send('Unauthorized');
      return
    }

    const imagesData = await mysql.awaitQuery(`select mvp_id, mvp_disc, mvp_dt,mvp_image,mb_nickname,mvp_lat, mvp_lng from maptile_vendor_pics left join maptile_building on mvp_building_id=mb_id where mvp_id in (?)`, [photoIds]);
    if (!imagesData.length) {
      res.status(404).send('No images found');
      return;
    }
    let images = [];
    for (let image of imagesData) {
      let url = image.mvp_image;
      let desc = image.mvp_disc?.replace(/^[\r\n]+|[\r\n]+$/g, "");

      //use mvp_lat and mvp_lng to find timezone and then convert mvp_dt to local time before sending to template
      let tz = find(image.mvp_lat, image.mvp_lng).toString();
      console.log(tz)
      let imgdate = moment.unix(image.mvp_dt).tz(tz).format('MM-DD-YYYY hh:mm A');
      //let imgdate = moment.unix(image.mvp_dt).format('MM/DD/YYYY hh:mm:ss A');
      images.push(new Array(image.mvp_id, desc, imgdate, url, image.mb_nickname))
    }
    const [logoData] = await mysql.awaitQuery(`select vendor_company_logo from maptile_vendors where vendor_id = ?`, [vendorId]);

    res.render('reports/photos-local-time-four-per-page', {
      images: images,
      logo: logoData
    });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }

}
const getFormReportExtended = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send('Unauthorized');
      return;
    }
    if (!req.query.start || !req.query.end) {
      console.error("Parameters missing");
      res.status(500).send('Parameters missing');
      return;
    }
    const reportQry = `SELECT
        sfs_created AS Date,
        sfs_uploader_email AS 'Submitter Email',
        sfs_form_name AS Form,
        mb_nickname AS Site,
        mb_address1 AS Address,
        (select City from maptile_city where CityId = mb_city) as City,
        (select state_abv_name from maptile_state where id = mb_state ) as State,
        mb_zip_code as Zip,
        (SELECT sf_contact_company_name FROM sitefotos_contacts_company_view WHERE sf_contact_id=mb_client) AS 'Client',
        (SELECT CONCAT(sf_contact_fname,' ',sf_contact_lname) FROM sitefotos_contacts WHERE sf_contact_id=mb_manager) AS 'Account Manager',
        (SELECT sf_contact_company_name  FROM sitefotos_contacts_company_view WHERE sf_contact_vendorid=? and JSON_CONTAINS(mb_contractor, JSON_ARRAY(CONVERT(sf_contact_id,CHAR))) LIMIT 1) AS 'SP Assigned',
        (SELECT sf_contact_email  FROM sitefotos_contacts WHERE sf_contact_vendorid=? and JSON_CONTAINS(mb_contractor, JSON_ARRAY(CONVERT(sf_contact_id,CHAR))) LIMIT 1) AS 'SP Email',
        (SELECT sf_contact_phone  FROM sitefotos_contacts WHERE sf_contact_vendorid=? and JSON_CONTAINS(mb_contractor, JSON_ARRAY(CONVERT(sf_contact_id,CHAR))) LIMIT 1) AS 'SP Phone',
        sfs_id as Submission
        
        FROM sitefotos_forms_submitted
        LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
        WHERE sfs_vendor_id = ?
        AND sfs_created <= ?
        AND sfs_created >= ?
        ORDER BY Date DESC
      `

    const data = await mysql.awaitQuery(reportQry, [vendorId, vendorId, vendorId, vendorId, req.query.end, req.query.start]);
    res.json(data)
  }
  catch (ex) {
    console.error(ex);
    next(ex)
  }
}
//vpics/getmaterialrangedata
const getMaterialRangeData = async (req, res, next) => {
  try {
    const token = req.cookies.JWT;
    const accessCode = req.query.accessCode || req.body.accessCode;
    const { vendorId, isHybrid, isClientViewEnabled } = await login(req.cookies.JWT, accessCode);
    if (!vendorId) {
      return res.sendStatus(401);
    }

    const start = req.query.start || req.body.start;
    const end = req.query.end || req.body.end;
    let query = `
      SELECT  swmd.*,sf.sf_trade_id from sitefotos_wp_material_data swmd left join sitefotos_forms sf on swmd_profile_id=sf_id
        WHERE swmd_vendor_id = ${vendorId} AND swmd_date_time <= ? AND swmd_date_time >= ? AND swmd_active = 1
      `;
    if (isClientViewEnabled) {
      query = `SELECT  swmd.*,sf.sf_trade_id from sitefotos_wp_material_data swmd left join sitefotos_forms sf on swmd_profile_id=sf_id where swmd_site_id in (SELECT mb_id FROM maptile_building WHERE mb_user_id=${vendorId} AND mb_user_type = '1' AND mb_status='1' UNION SELECT mb_id FROM maptile_building mb JOIN sitefotos_site_client_mapping_extended_ungrouped scme ON mb.mb_id = scme.sscm_site_id WHERE scme.scs_client_vendor_id = ${vendorId} AND mb_user_type = '1' AND mb_status='1') AND swmd_date_time <= ? AND swmd_date_time >= ? AND swmd_active=1`;

    }
    const params = [end, start];
    const rows = await mysql.awaitSafeQuery(query, params);
    res.json(rows);

  } catch (error) {
    next(error);
  }
};
//vpics/getservicerangedata
const getServiceRangeData = async (req, res, next) => {
  try {
    if (!req.query.start || !req.query.end || !req.query.accessCode) {
      console.error("Parameters missing");
      res.status(500).send('Parameters missing');
      return;
    }
    const accessCode = req.query.accessCode;
    const start = req.query.start;
    const end = req.query.end;

    const { vendorId, internalUid, isHybrid, isClientViewEnabled } = await login(req.cookies.JWT, accessCode);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send('Unauthorized');
      return;
    }

    let qry = `SELECT * from sitefotos_wp_services_data LEFT join vendor_services ON swsd_service_id=vs_service_id WHERE (swsd_vendor_id= '${vendorId}' OR swsd_uploader_vid='${vendorId}') AND swsd_date_time <= ${end} AND swsd_date_time >= ${start} AND swsd_active=1`;
    if (isHybrid) {
      qry = `SELECT * from sitefotos_wp_services_data LEFT join vendor_services ON swsd_service_id=vs_service_id where vs_vendor_id='${vendorId}' and swsd_vendor_id in (select sf_contact_hybrid_contractor_vendor_id from sitefotos_contacts where sf_contact_vendorid = '${vendorId}' and sf_contact_hybrid_contractor ='Y') AND swsd_date_time <= ${end} AND swsd_date_time >= ${start} AND swsd_active=1`;
    }
    if (isClientViewEnabled) {
      if (internalUid && internalUid != 0) {
        let buildings = await awaitSafeQuery(`select * from sitefotos_subuser_configuration where ssc_user_id=?`, [internalUid])
        let buildingIds = buildings.map((item) => item.ssc_data)[0]
        if (buildingIds.length == 0) {
          console.error("No buildings found for this user", internalUid);
          return res.send([]);
        }
        qry = `SELECT * from sitefotos_wp_services_data LEFT join vendor_services ON swsd_service_id=vs_service_id where swsd_site_id in (${buildingIds}) AND swsd_date_time <= ${end} AND swsd_date_time >= ${start} AND swsd_active=1 AND swsd_service_status NOT IN ('REMOVEDEMAIL', 'TEMPLOG', 'REJECTED')`;
      }
      else {
        qry = `SELECT * from sitefotos_wp_services_data LEFT join vendor_services ON swsd_service_id=vs_service_id where swsd_site_id in (SELECT mb_id FROM maptile_building WHERE mb_user_id=${vendorId} AND mb_user_type = '1' AND mb_status='1' UNION SELECT mb_id FROM maptile_building mb JOIN sitefotos_site_client_mapping_extended_ungrouped scme ON mb.mb_id = scme.sscm_site_id WHERE scme.scs_client_vendor_id = ${vendorId} AND mb_user_type = '1' AND mb_status='1') AND swsd_date_time <= ${end} AND swsd_date_time >= ${start} AND swsd_active=1 AND swsd_service_status NOT IN ('REMOVEDEMAIL', 'TEMPLOG', 'REJECTED')`;
      }

    }

    const result = await mysql.awaitQuery(qry, []);
    return res.json(result);
  } catch (error) {
    next(error);
  }
};
const getClientContractTypesBySites = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT)
    const sql = `
    select
      sps_contract_type,
      sps_site_id
    from
      sitefotos_pricing_structure
    left join maptile_building on
      sps_site_id = mb_id
    where
      sps_vendor_id = ?
      and sps_contract_active = '1'
      and sps_contract_for = 'CLIENT' group by sps_site_id
    `
    const result = await mysql.awaitQuery(sql, [vendorId]);
    res.json(result);
  }
  catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getServiceTrackerBySites = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const services = await mysql.awaitQuery(/*SQL*/`select swsd_id as service_id, mb_nickname as site_name, mb_id as site_id, swsd_service_name as service_name, svc.swsd_date_time as service_time, svc.swsd_form_submission_reference as submission_id, mb_address1 as address, (select vendor_company_name from maptile_vendors where vendor_id=mb_user_id) as Service_Provider, mb_zip_code as zip, ( select City from maptile_city where CityId = mb_city) as city, ( select state_name from maptile_state where id = mb_state) as state, mb_global_strategic_manager as Account_Manager, mb_global_senior_manager as Facility_Manager, mb_global_regional_manager as Service_Model, mb_global_customer_location_id as Property_Status, mb_global_contract_type as Markets, mb_vendor_internal_id as id from sitefotos_wp_services_data svc inner join maptile_building on mb_id = svc.swsd_site_id inner join vendor_services on vs_service_id = svc.swsd_service_id where mb_hybrid_vendor_id = ?  and swsd_date_time>? and swsd_date_time<? and swsd_vendor_id in (select sf_contact_hybrid_contractor_vendor_id from sitefotos_contacts where sf_contact_vendorid = ? and sf_contact_hybrid_contractor ='Y') group by swsd_id`, [vendorId, req.query.start, req.query.end, vendorId]);

    const uniqueSubmissions = [...new Set(services.map(item => item.submission_id))];
    if (uniqueSubmissions.length == 0) {
      res.json({ services: [], photos: [] });
      return
    }
    const photos = await mysql.awaitQuery(/*SQL*/`SELECT photo, sfs_id, sfs_form_id from sitefotos_forms_submitted, JSON_TABLE(sfs_form_data_full,'$.pages[*].elements[*]' COLUMNS(id FOR ORDINALITY,type VARCHAR(40) PATH "$.type",NESTED PATH '$.value[*]' COLUMNS (photo VARCHAR(255) PATH '$.lrImageURL'))) abc where sfs_id in (?) and type='file' and photo is not null`, [uniqueSubmissions]);
    res.json({ services, photos });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getServiceTrackerBySitesClient = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let buildings = req.body.buildings;

    const services = await mysql.awaitQuery(/*SQL*/`select
    swsd_id as service_id,
    mb_nickname as site_name,
    mb_id as site_id,
    swsd_service_name as service_name,
    sst_service_type as serivce_type,
    svc.swsd_date_time as service_time,
    svc.swsd_form_submission_reference as submission_id,
    mb_address1 as address,
    (
    select
      vendor_company_name
    from
      maptile_vendors
    where
      vendor_id = mb_user_id) as Service_Provider,
    mb_zip_code as zip,
    (
    select
      City
    from
      maptile_city
    where
      CityId = mb_city) as city,
    (
    select
      state_name
    from
      maptile_state
    where
      id = mb_state) as state,
    mb_vendor_internal_id as id,
    scs_client_internal_id,
    scsd_region,
    scsd_property_manager,
    scsd_senior_property_manager,
    scsd_regional_manager,
    scsd_location_type,
    scsd_leased_owned,
    scsd_contract_type
  from
    sitefotos_wp_services_data svc
  left join maptile_building on
    mb_id = svc.swsd_site_id
  left join vendor_services on
    vs_service_id = svc.swsd_service_id
  left join sitefotos_service_types on
    sst_id = vs_service_type_id
  left join sitefotos_site_client_mapping_extended_ungrouped on
    sscm_site_id = mb_id
  left join sitefotos_client_sites_data on
    sscm_client_site_id = scsd_client_site_id
  where
    swsd_date_time>?
    and swsd_date_time<?
    and swsd_site_id in (?)
    and swsd_service_status NOT IN ('REMOVEDEMAIL', 'TEMPLOG', 'REJECTED')
  group by
    swsd_id`, [req.query.start, req.query.end, buildings]);

    const uniqueSubmissions = [...new Set(services.map(item => item.submission_id))];
    if (uniqueSubmissions.length == 0) {
      res.json({ services: [], photos: [] });
      return
    }
    const photos = await mysql.awaitQuery(/*SQL*/`SELECT photo, sfs_id, sfs_form_id, sfs_building_id from sitefotos_forms_submitted, JSON_TABLE(sfs_form_data_full,'$.pages[*].elements[*]' COLUMNS(id FOR ORDINALITY,type VARCHAR(40) PATH "$.type",NESTED PATH '$.value[*]' COLUMNS (photo VARCHAR(255) PATH '$.lrImageURL'))) abc where sfs_id in (?) and type='file' and photo is not null`, [uniqueSubmissions]);
    res.json({ services, photos });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getWinterServiceTrackerWellsFargo = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let buildings = req.body.buildings;
    const allowedServiceNames = ['Plow Lots', 'Shovel Walks', 'De-ice Lots', 'De-ice Walks', 'Snow Hauling']
    const allowedServiceTypes = [100, 101,103,104,114];
    let mapping = await getSnowVendorMapping();
    let uniqueVendors = [...new Set(mapping.map(item => item.vendor_id))];
    let services = await mysql.awaitQuery(/*SQL*/`select
    swsd_id as service_id,
    mb_nickname as site_name,
    mb_id as site_id,
    swsd_service_name as service_name,
    sst_service_type as serivce_type,
    svc.swsd_date_time as service_time,
    svc.swsd_form_submission_reference as submission_id,
    mb_address1 as address,
    (
    select
      vendor_company_name
    from
      maptile_vendors
    where
      vendor_id = mb_user_id) as Service_Provider,
    mb_zip_code as zip,
    (
    select
      City
    from
      maptile_city
    where
      CityId = mb_city) as city,
    (
    select
      state_name
    from
      maptile_state
    where
      id = mb_state) as state,
    mb_vendor_internal_id as id,
    scs_client_internal_id,
    scsd_region,
    scsd_property_manager,
    scsd_senior_property_manager,
    scsd_regional_manager,
    scsd_location_type,
    scsd_leased_owned,
    scsd_contract_type
  from
    sitefotos_wp_services_data svc
  left join maptile_building on
    mb_id = svc.swsd_site_id
  left join vendor_services on
    vs_service_id = svc.swsd_service_id
  left join sitefotos_service_types on
    sst_id = vs_service_type_id
  left join sitefotos_site_client_mapping_extended_ungrouped on
    sscm_site_id = mb_id
  left join sitefotos_client_sites_data on
    sscm_client_site_id = scsd_client_site_id
  left join sitefotos_forms on sf_id = swsd_profile_id
  where
    swsd_date_time>?
    and swsd_date_time<?
    and swsd_site_id in (?)
    and vs_service_type_id in (?)
    and sf_vendor_id in (?)
  `, [req.query.start, req.query.end, buildings, allowedServiceTypes, uniqueVendors]);

    const uniqueSubmissions = [...new Set(services.map(item => item.submission_id))];
    if (uniqueSubmissions.length == 0) {
      res.json({ services: [], photos: [] });
      return
    }
   
    res.json({ services, mapping });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getWellsFargoDPREvents = async(req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let events = await mysql.awaitQuery(`select swo_form_template_id as value, sf_form_name as text from sitefotos_work_orders left join sitefotos_forms on swo_form_template_id=sf_id where swo_vendor_id = 15084 and swo_bulk= '1' group by swo_form_template_id ;
    `, []);
    res.json(events);
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}
const getVerizonJanitorialChartDataTime = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);

    if (vendorId != 5260) {
      return res.status(401).send('Unauthorized');
    }

    const monthOffset = parseInt(req.query.monthOffset) || 0;
    const weekOffset = parseInt(req.query.weekOffset) || 0;

    const monthStart = new Date();
    monthStart.setMonth(monthStart.getMonth() - monthOffset, 1);
    monthStart.setHours(0, 0, 0, 0);
    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthEnd.getMonth() + 1);


    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const daysToSubtract = currentDay === 0 ? 7 : currentDay;
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - daysToSubtract);
    startOfWeek.setHours(0, 0, 0, 0);

    const weekStart = new Date(startOfWeek);
    weekStart.setDate(weekStart.getDate() - 7 * weekOffset);

    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekEnd.getDate() + 7);

    const [weeklyData] = await mysql.awaitCachedQuery(/*SQL*/`WITH unique_sites AS (
      SELECT
        mb.mb_vendor_internal_id,
        mb.mb_water_utility,
        mb.mb_hybrid_vendor_id,
        mb.mb_id
      FROM
        maptile_building mb
      WHERE
        mb.mb_hybrid_vendor_id = 5260 and mb.mb_water_utility is not null),
      cleaned_sites AS (
      SELECT
        mbs.mb_vendor_internal_id,
        COUNT(*) AS clean_count
      FROM
        sitefotos_forms_submitted sfs
      JOIN sitefotos_forms sf ON
        sfs.sfs_form_id = sf.sf_id
      LEFT join maptile_building mbs on sfs.sfs_building_id = mb_id 
      WHERE
        sf.sf_trade_id = 2
        AND sf.sf_form_sharer_vendor_id = 5260
        AND sfs.sfs_created >= UNIX_TIMESTAMP(?)
          AND sfs.sfs_created < UNIX_TIMESTAMP(?)
        GROUP BY 
          mbs.mb_vendor_internal_id )
      SELECT
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '1W' THEN us.mb_vendor_internal_id END) AS unique_1W_sites_cleaned,
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '2W' AND cs.clean_count >= 2 THEN us.mb_vendor_internal_id END) AS unique_2W_sites_cleaned_twice,
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '3W' AND cs.clean_count >= 3 THEN us.mb_vendor_internal_id END) AS unique_3W_sites_cleaned_three_times,
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '5W' AND cs.clean_count >= 5 THEN us.mb_vendor_internal_id END) AS unique_5W_sites_cleaned_five_times,
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '6W' AND cs.clean_count >= 6 THEN us.mb_vendor_internal_id END) AS unique_6W_sites_cleaned_six_times,
        (
        SELECT
          COUNT(DISTINCT mb1.mb_vendor_internal_id)
        FROM
          unique_sites mb1
        WHERE
          mb1.mb_water_utility = '1W') AS total_unique_1W_sites,
        (
        SELECT
          COUNT(DISTINCT mb2.mb_vendor_internal_id)
        FROM
          unique_sites mb2
        WHERE
          mb2.mb_water_utility = '2W') AS total_unique_2W_sites,
        (
        SELECT
          COUNT(DISTINCT mb3.mb_vendor_internal_id)
        FROM
          unique_sites mb3
        WHERE
          mb3.mb_water_utility = '3W') AS total_unique_3W_sites,
        (
        SELECT
          COUNT(DISTINCT mb5.mb_vendor_internal_id)
        FROM
          unique_sites mb5
        WHERE
          mb5.mb_water_utility = '5W') AS total_unique_5W_sites,
        (
        SELECT
          COUNT(DISTINCT mb6.mb_vendor_internal_id)
        FROM
          unique_sites mb6
        WHERE
          mb6.mb_water_utility = '6W') AS total_unique_6W_sites
      FROM
        cleaned_sites cs
      JOIN unique_sites us ON cs.mb_vendor_internal_id = us.mb_vendor_internal_id`, [weekStart.toISOString().slice(0, 19).replace('T', ' '), weekEnd.toISOString().slice(0, 19).replace('T', ' ')]);


    const [monthlyData] = await mysql.awaitCachedQuery(/*SQL*/`WITH unique_sites AS (
      SELECT
        mb.mb_vendor_internal_id,
        mb.mb_water_utility,
        mb.mb_hybrid_vendor_id,
        mb.mb_id
      FROM
        maptile_building mb
      WHERE
        mb.mb_hybrid_vendor_id = 5260 and mb.mb_water_utility is not null ),
      cleaned_sites AS (
      SELECT
        mbs.mb_vendor_internal_id,
        COUNT(*) AS clean_count
      FROM
        sitefotos_forms_submitted sfs
      JOIN sitefotos_forms sf ON
        sfs.sfs_form_id = sf.sf_id
      LEFT join maptile_building mbs on sfs.sfs_building_id = mb_id 
      WHERE
        sf.sf_trade_id = 2
        AND sf.sf_form_sharer_vendor_id = 5260
        AND sfs.sfs_created >= UNIX_TIMESTAMP(?)
          AND sfs.sfs_created < UNIX_TIMESTAMP(?)
        GROUP BY
          mbs.mb_vendor_internal_id )
      SELECT
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '1M' THEN us.mb_vendor_internal_id END) AS unique_1M_sites_cleaned,
        COUNT(DISTINCT CASE WHEN us.mb_water_utility = '2M' AND cs.clean_count >= 2 THEN us.mb_vendor_internal_id END) AS unique_2M_sites_cleaned_twice,
        (
        SELECT
          COUNT(DISTINCT mb1.mb_vendor_internal_id)
        FROM
          unique_sites mb1
        WHERE
          mb1.mb_water_utility = '1M') AS total_unique_1M_sites,
        (
        SELECT
          COUNT(DISTINCT mb2.mb_vendor_internal_id)
        FROM
          unique_sites mb2
        WHERE
          mb2.mb_water_utility = '2M') AS total_unique_2M_sites
      FROM
        cleaned_sites cs
      JOIN unique_sites us ON cs.mb_vendor_internal_id = us.mb_vendor_internal_id
    `, [monthStart.toISOString().slice(0, 19).replace('T', ' '), monthEnd.toISOString().slice(0, 19).replace('T', ' ')]);
    res.json({ weeklyData, monthlyData });
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};


const getVerizonLandscapeChartData = async (req, res, next) => {

  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    if (vendorId != 5260) {
      return res.status(401).send('Unauthorized');
    }

    const weeklyOffset = parseInt(req.query.weekOffset) || 0;
    const monthlyOffset = parseInt(req.query.monthOffset) || 0;



    const monthStart = `UNIX_TIMESTAMP(DATE_FORMAT(NOW() - INTERVAL ${monthlyOffset} MONTH, '%Y-%m-01 00:00:00'))`;
    const monthEnd = `UNIX_TIMESTAMP(DATE_FORMAT(NOW() - INTERVAL ${monthlyOffset - 1} MONTH, '%Y-%m-01 00:00:00'))`;


    const [monthlyData] = await mysql.awaitCachedQuery(/*SQL*/`WITH unique_sites AS (
      SELECT 
        mb.mb_vendor_internal_id, 
        mb.mb_gas_utility, 
        mb.mb_hybrid_vendor_id, 
        mb.mb_id 
      FROM 
        maptile_building mb 
      WHERE 
        mb.mb_hybrid_vendor_id = 5260
        AND mb.mb_gas_utility IS NOT NULL
    ), 
    cleaned_sites AS (
      SELECT 
      mbs.mb_vendor_internal_id, 
        COUNT(*) AS clean_count, 
        MAX(
          CASE WHEN DAYOFMONTH(
            FROM_UNIXTIME(sfs.sfs_created)
          ) <= 15 THEN 1 ELSE 0 END
        ) AS first_half_cleaned, 
        MAX(
          CASE WHEN DAYOFMONTH(
            FROM_UNIXTIME(sfs.sfs_created)
          ) > 15 THEN 1 ELSE 0 END
        ) AS second_half_cleaned 
      FROM 
        sitefotos_forms_submitted sfs 
        JOIN sitefotos_forms sf ON sfs.sfs_form_id = sf.sf_id 
        LEFT join maptile_building mbs on sfs.sfs_building_id = mb_id
      WHERE 
        sf.sf_trade_id = 3 
        AND mbs.mb_hybrid_vendor_id = 5260
        AND mbs.mb_gas_utility IS NOT NULL
        AND sf.sf_form_sharer_vendor_id = 5260 
        AND sfs.sfs_created >= ${monthStart} 
        AND sfs.sfs_created < ${monthEnd} 
      GROUP BY 
      mbs.mb_vendor_internal_id
    ) 
    SELECT 
      COUNT(
        DISTINCT CASE WHEN us.mb_gas_utility = '1M' THEN us.mb_vendor_internal_id END
      ) AS unique_1M_sites_cleaned, 
      (
        SELECT 
          COUNT(
            DISTINCT mb1.mb_vendor_internal_id
          ) 
        FROM 
          unique_sites mb1 
        WHERE 
          mb1.mb_gas_utility = '1M'
      ) AS total_unique_1M_sites, 
      COUNT(
        DISTINCT CASE WHEN us.mb_gas_utility = '3M' 
        AND cs.clean_count >= 3 THEN us.mb_vendor_internal_id END
      ) AS unique_3M_sites_cleaned_three_times, 
      (
        SELECT 
          COUNT(
            DISTINCT mb3.mb_vendor_internal_id
          ) 
        FROM 
          unique_sites mb3 
        WHERE 
          mb3.mb_gas_utility = '3M'
      ) AS total_unique_3M_sites, 
      COUNT(
        DISTINCT CASE WHEN us.mb_gas_utility = 'EOW' 
        AND (
          (
            DAYOFMONTH(NOW()) <= 15 
            AND cs.first_half_cleaned = 1
          ) 
          OR (
            DAYOFMONTH(NOW()) > 15 
            AND cs.second_half_cleaned = 1
          )
        ) THEN us.mb_vendor_internal_id END
      ) AS unique_EOW_sites_cleaned_current_half, 
      (
        SELECT 
          COUNT(
            DISTINCT mb4.mb_vendor_internal_id
          ) 
        FROM 
          unique_sites mb4 
        WHERE 
          mb4.mb_gas_utility = 'EOW'
      ) AS total_unique_EOW_sites 
    FROM 
      cleaned_sites cs 
      JOIN unique_sites us ON cs.mb_vendor_internal_id = us.mb_vendor_internal_id
    `, []);

    res.json({ monthlyData });
  } catch (ex) {
    console.error(ex);
    next(ex);
  }

}

const getVerizonTabulatedLandScapeData = async (req, res, next) => {
  try {
    const { type, monthOffset } = req.query;

    if (!['1M', '3M'].includes(type)) {
      return res.status(400).send('Invalid parameter');
    }

    const monthStart = `UNIX_TIMESTAMP(DATE_FORMAT(NOW() - INTERVAL ${parseInt(monthOffset)} MONTH, '%Y-%m-01 00:00:00'))`;
    const monthEnd = `UNIX_TIMESTAMP(DATE_FORMAT(NOW() - INTERVAL ${parseInt(monthOffset) - 1} MONTH, '%Y-%m-01 00:00:00'))`;

    // Query to fetch tabulated data
    const tabulatedData = await mysql.awaitCachedQuery(/*SQL*/`
      WITH unique_sites AS (
        SELECT mb.mb_id,mb.mb_vendor_internal_id, (select City from maptile_city where CityId = mb_city)
        as cityname, (select state_abv_name from maptile_state
        where id = mb_state ) as statename, mb_nickname, mb.mb_gas_utility, mb.mb_hybrid_vendor_id, GROUP_CONCAT(mb.mb_id) AS mb_ids, group_concat(mv.vendor_company_name) as vendors
        FROM maptile_building mb 
        left join maptile_vendors mv on  mv.vendor_id = mb.mb_user_id
        WHERE mb.mb_hybrid_vendor_id = 5260
        AND mb.mb_gas_utility IS NOT NULL
        group by mb.mb_vendor_internal_id
      ), 
      cleaned_sites AS (
        SELECT mbs.mb_vendor_internal_id, COUNT(*) AS clean_count, group_concat(sfs_id) as submissions
        FROM sitefotos_forms_submitted sfs 
        JOIN sitefotos_forms sf ON sfs.sfs_form_id = sf.sf_id 
        LEFT join maptile_building mbs on sfs.sfs_building_id = mb_id
        WHERE sf.sf_trade_id = 3 
        AND sf.sf_form_sharer_vendor_id = 5260 
        AND sfs.sfs_created >= ${monthStart} 
        AND sfs.sfs_created < ${monthEnd} 
        GROUP BY mbs.mb_vendor_internal_id
      )
      SELECT 
        us.mb_vendor_internal_id, 
        us.mb_gas_utility, 
        us.mb_nickname,
        us.cityname,
        us.statename,
        us.vendors,
        cs.submissions,
        COALESCE(cs.clean_count, 0) AS clean_count,
        CASE 
          WHEN (us.mb_gas_utility = '1M' AND COALESCE(cs.clean_count, 0) >= 1) OR 
               (us.mb_gas_utility = '3M' AND COALESCE(cs.clean_count, 0) >= 3) 
          THEN 'COMPLETED' 
          ELSE 'NOT COMPLETED' 
        END AS completion_status
      FROM unique_sites us
      LEFT JOIN cleaned_sites cs ON us.mb_vendor_internal_id = cs.mb_vendor_internal_id
      WHERE us.mb_gas_utility = ?
    `, [type]);

    res.json({ tabulatedData });
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};

const getVerizonTabulatedJanitorialData = async (req, res, next) => {
  try {
    const { type, monthOffset, weekOffset } = req.query;

    if (!['1W', '2W', '3W', '5W', '6W', '1M', '2M'].includes(type)) {
      return res.status(400).send('Invalid parameter');
    }

    let start, end;
    if (type.includes('W')) {
      // Calculate weekly range
      const currentDate = new Date();
      const currentDay = currentDate.getDay();
      const daysToSubtract = currentDay === 0 ? 7 : currentDay;
      const startOfWeek = new Date(currentDate);
      startOfWeek.setDate(currentDate.getDate() - daysToSubtract);
      startOfWeek.setHours(0, 0, 0, 0);

      start = new Date(startOfWeek);
      start.setDate(start.getDate() - 7 * parseInt(weekOffset));

      end = new Date(start);
      end.setDate(end.getDate() + 7);
    } else if (type.includes('M')) {
      // Calculate monthly range
      start = new Date();
      start.setMonth(start.getMonth() - parseInt(monthOffset), 1);
      start.setHours(0, 0, 0, 0);

      end = new Date(start);
      end.setMonth(end.getMonth() + 1);
    }

    const startTimestamp = `UNIX_TIMESTAMP('${start.toISOString().slice(0, 19).replace('T', ' ')}')`;
    const endTimestamp = `UNIX_TIMESTAMP('${end.toISOString().slice(0, 19).replace('T', ' ')}')`;

    // Query to fetch tabulated janitorial data
    const tabulatedData = await mysql.awaitCachedQuery(/*SQL*/`
      WITH unique_sites AS (
        SELECT 
          mb.mb_vendor_internal_id, 
          mb.mb_id,
          (SELECT City FROM maptile_city WHERE CityId = mb_city) AS cityname, 
          (SELECT state_abv_name FROM maptile_state WHERE id = mb_state) AS statename, 
          mb_nickname, 
          mb.mb_water_utility, 
          mb.mb_hybrid_vendor_id, 
          GROUP_CONCAT(mb.mb_id) AS mb_ids, 
          GROUP_CONCAT(mv.vendor_company_name) AS vendors
        FROM 
          maptile_building mb 
          LEFT JOIN maptile_vendors mv ON mv.vendor_id = mb.mb_user_id
        WHERE 
          mb.mb_hybrid_vendor_id = 5260
          and mb.mb_water_utility is not null
        GROUP BY 
          mb.mb_vendor_internal_id
      ), 
      cleaned_sites AS (
        SELECT 
          mbs.mb_vendor_internal_id, 
          COUNT(*) AS clean_count, 
          GROUP_CONCAT(sfs_id) AS submissions
        FROM 
          sitefotos_forms_submitted sfs 
          JOIN sitefotos_forms sf ON sfs.sfs_form_id = sf.sf_id 
          LEFT JOIN maptile_building mbs ON sfs.sfs_building_id = mb_id
        WHERE 
          sf.sf_trade_id = 2 
          AND sf.sf_form_sharer_vendor_id = 5260 
          AND sfs.sfs_created >= ${startTimestamp} 
          AND sfs.sfs_created < ${endTimestamp} 
        GROUP BY 
          mbs.mb_vendor_internal_id
      )
      SELECT 
        us.mb_vendor_internal_id, 
        us.mb_water_utility, 
        us.mb_nickname,
        us.cityname,
        us.statename,
        us.vendors,
        cs.submissions,
        COALESCE(cs.clean_count, 0) AS clean_count,
        CASE 
          WHEN (us.mb_water_utility = '1W' AND COALESCE(cs.clean_count, 0) >= 1) OR 
               (us.mb_water_utility = '2W' AND COALESCE(cs.clean_count, 0) >= 2) OR
               (us.mb_water_utility = '3W' AND COALESCE(cs.clean_count, 0) >= 3) OR
               (us.mb_water_utility = '5W' AND COALESCE(cs.clean_count, 0) >= 5) OR
               (us.mb_water_utility = '6W' AND COALESCE(cs.clean_count, 0) >= 6) OR
               (us.mb_water_utility = '1M' AND COALESCE(cs.clean_count, 0) >= 1) OR 
               (us.mb_water_utility = '2M' AND COALESCE(cs.clean_count, 0) >= 2) 
          THEN 'COMPLETED' 
          ELSE 'NOT COMPLETED' 
        END AS completion_status
      FROM 
        unique_sites us
        LEFT JOIN cleaned_sites cs ON us.mb_vendor_internal_id = cs.mb_vendor_internal_id
      WHERE 
        us.mb_water_utility = ?
    `, [type]);

    res.json({ tabulatedData });
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};


const getWellsFargoDPREventData = async(req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let data = await mysql.awaitQuery(`SELECT sfs_id, sfs_form_id, question_title, selected_choice, question_type, mb.mb_id, mb.mb_nickname, mb_lat, mb_long, (select City from maptile_city where CityId = mb_city) as City, (select state_abv_name from maptile_state where id = mb_state ) as State, mb_zip_code as Zip FROM sitefotos_forms_submitted sfs left join maptile_building mb ON mb.mb_id = sfs.sfs_building_id, JSON_TABLE( sfs_form_data_full, '$.pages[*].elements[*]' COLUMNS ( question_title VARCHAR(255) PATH '$.title', selected_choice VARCHAR(255) PATH '$.value', choices_json JSON PATH '$.choices',question_type VARCHAR(255) PATH '$.type') ) AS questions, JSON_TABLE( questions.choices_json, '$[*]' COLUMNS ( choice VARCHAR(255) PATH '$' ) ) AS choices WHERE sfs_form_id = ? GROUP BY sfs_id, question_title`, [req.params.event_id]);
    data = data.filter(item => item.question_type === 'radiogroup');

    let completionData = await mysql.awaitQuery(`select sf_id,sf_active from sitefotos_forms left join sitefotos_work_orders_submissions on swos_workorder_id=sf_form_workticket_id where sf_form_workticket_template_id=? and sf_active='3' and swos_id is not null group by swos_workorder_id`, [req.params.event_id]);

    let allSitesData = await mysql.awaitQuery(`select mb_id, mb_nickname, mb_lat, mb_long, (select City from maptile_city where CityId = mb_city) as City, (select state_abv_name from maptile_state where id = mb_state ) as State, mb_zip_code as Zip from sitefotos_work_orders left join maptile_building on mb_id=swo_site_id where swo_form_template_id = ? and swo_active=1 and swo_status <> 6`,[req.params.event_id])

    res.json({data, completionData, allSitesData});
  } catch (ex) {
    console.error(ex);
    next(ex)
  }

}
const getStormsInTimeRange = async (req, res, next) => {
  const { start, end } = req.query;
  const { vendorId, internalUid } = await login(req.cookies.JWT);

  try {
    const users = await mysql.awaitSafeQuery("select * from sitefotos_apikeys where sfapi_provider in ('WW', 'TW') and sfapi_vendor_id=?)", [vendorId]);
    if (users.length === 0) {
      return res.json([]);
    }
    const result = await postgres.awaitQuery(
      "SELECT DISTINCT wssr_storm_id, wssr_start_time, wssr_end_time FROM weather_station_storms_readings LEFT OUTER JOIN weather_stations_mapping ON wssr_ws_external_id=wsg_ws_external_id WHERE wsg_vid=$1 AND wssr_start_time>=$2 AND wssr_end_time<=$3 AND wsg_owned=1",
      [vendorId, start, end]
    );
    res.json(result);
  } catch (error) {
    return res.status(500).send({ error: error.message });
  }
};
const getStormsInTimeRangeProvider = async (req, res, next) => {
  const { start, end, provider } = req.query;
  const { vendorId, internalUid } = await login(req.cookies.JWT);

  try {
    const users = await mysql.awaitSafeQuery("select * from sitefotos_apikeys where sfapi_provider=? and sfapi_vendor_id=?)", [provider, vendorId]);
    if (users.length === 0) {
      return res.json([]);
    }
    const providerId = provider === 'WW' ? 101 : 102;

    let dataQuery = `
          SELECT wsg_bid AS sfb_building_id,
          SUM(COALESCE(NULLIF(REGEXP_REPLACE(wssr_total, '[^0-9.]*', '', 'g'), ''), '0')::decimal) AS snow_total
          FROM weather_stations_mapping
          LEFT JOIN weather_station_storms_readings ON wsg_ws_external_id = wssr_ws_external_id
          WHERE wsg_vid=$1
          AND wsg_provider_id = $2
          AND wssr_start_time>=$3 AND wssr_end_time<=$4
          GROUP BY wsg_bid`;

    let data = await postgres.awaitQuery(dataQuery, [vendorId, providerId, start, end]);
    res.json(data);

  } catch (error) {
    return res.status(500).send({ error: error.message });
  }
};
const getSnowStorms = async (req, res, next) => {
  try {
    const { start, end } = req.query;
    const { vendorId, internalUid } = await login(req.cookies.JWT);


    const rows = await mysql.awaitQuery("select * from sitefotos_apikeys where (sfapi_provider='WW' OR sfapi_provider='TW' and sfapi_vendor_id=?)", [vendorId]);


    if (rows.length === 0) {
      return res.status(403).send("User does not have access configured");
    }


    const snowstormsQuery = `SELECT wssr.*, wsm.*, ws.*, tz.tzid as time_zone FROM weather_station_storms_readings wssr INNER JOIN weather_stations_mapping wsm ON wssr.wssr_ws_external_id = wsm.wsg_ws_external_id and wssr.wssr_provider_id = wsm.wsg_provider_id INNER JOIN weather_stations ws ON wsm.wsg_ws_external_id = ws.ws_external_id INNER JOIN timezones tz ON ST_Contains(tz.geom, ST_MakePoint(ST_X(ws.ws_latlng), ST_Y(ws.ws_latlng))) WHERE wsm.wsg_vid = $1 AND  wsm.wsg_owned = 1 AND wssr.wssr_start_time > $2 AND wssr.wssr_end_time < $3`;
    const snowstormsRows = await postgres.awaitQuery(snowstormsQuery, [vendorId, start, end]);

    res.json(snowstormsRows);
  } catch (error) {

    next(error);
  }
};

const getCameras = async (req, res, next) => {
  try {

    const rows = await postgres.awaitQuery(`SELECT c_id, c_type, c_videotype, ST_X(c_latlng) as lng, ST_Y(c_latlng) as lat 
      FROM sitefotos_cameras 
      WHERE c_enabled='1' AND is_dead=false`);
    res.json(rows);
  } catch (ex) {
    next(ex)
  }
};
const getWellsFargoPieReport = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let form = await awaitSafeQuery(`select * from sitefotos_forms where sf_form_name = 'WF - Landscape Weekly Form' and sf_vendor_id = ?`, [vendorId]);
    if (form.length == 0) {
      res.json([]);
      return
    }
    let buildings = await mysql.awaitQuery(`select mb_id from sitefotos_site_client_mapping_extended_ungrouped left join maptile_building on mb_id=sscm_site_id where scs_client_vendor_id =15183 and sscm_vendor_id = ? and FIND_IN_SET(mb_contract_type,'Landscaping')`, [vendorId]);
    buildings = buildings.map(item => item.mb_id);
    if (buildings.length == 0) {
      res.json([]);
      return
    }
    //start date sunday 12:00 am
    let start = moment().startOf('week').subtract(1, 'days').format('X');
    //get submissions for the week
    let submissions = await mysql.awaitQuery(`select * from sitefotos_forms_submitted where sfs_form_id = ? and sfs_building_id in (?) and sfs_created > ?`, [form[0].sf_id, buildings, start]);
    //there should be one submission per building so ignore multiple submissions for the same building
    let uniqueSubmissions = [...new Set(submissions.map(item => item.sfs_building_id))];
    //assembly pie chart data for apexcharts to send to frontend for rendering buildings/submissions
    let data = {
      buildings: buildings.length,
      submissions: uniqueSubmissions.length
    }

    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}


//New Verizon Functions for the chart rollup reports
const getVerizonLandscapingData = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const week = req.query.week || 0;

    const cacheKey = `getVerizonLandscapingData:${week}:${vendorId}:${internalUid}`;
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
      res.json(JSON.parse(cachedData));
      return;
    }

    let forms = await awaitSafeQuery(`select sf_id from sitefotos_forms where sf_form_sharee_form_id in (30030, 303398)`);
    if (forms.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    let buildings = await reportService.getHybridUserSites(internalUid);
    if (buildings.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    const siteIdMap = new Map();
    const siteIds = [];
    buildings.forEach(site => {
      const trades = site.mb_contract_type ? site.mb_contract_type.split(';') : [];
      const contractors = site.site_id ? site.site_id.split(',') : [];
      trades.forEach((trade, index) => {
        if (trade.includes('Landscaping')) {
          const contractorIds = contractors[index].split(',');
          contractorIds.forEach(contractorId => {
            if (!siteIdMap.has(contractorId)) {
              siteIdMap.set(contractorId, site.site_id);
            }
            siteIds.push(contractorId);
          });
        }
      });
    });
    if (siteIds.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }
    const flatSiteIds = [...new Set(siteIds)];
    const start = moment().subtract(week * 7 + 1, 'days').startOf('week').unix();
    const end = moment().subtract(week * 7 - 6, 'days').startOf('week').unix();
    forms = forms.map(item => item.sf_id);

    let submissions = await mysql.awaitQuery(`select * from sitefotos_forms_submitted where sfs_form_id in (?) and sfs_building_id in (?) and sfs_created > ? and sfs_created < ?`, [forms, flatSiteIds, start, end]);

    let canonicalSubmissions = submissions.map(sub => siteIdMap.get(sub.sfs_building_id) || sub.sfs_building_id);
    let uniqueSubmissions = [...new Set(canonicalSubmissions)];

    let data = {
      buildings: siteIds.length,
      submissions: uniqueSubmissions.length
    };

    await redisClient.set(cacheKey, JSON.stringify(data), { 'EX': 1800 });
    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};

const getVerizonJanitorialData = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const week = req.query.week || 0;

    const cacheKey = `getVerizonJanitorialData:${week}:${vendorId}:${internalUid}`;
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
      res.json(JSON.parse(cachedData));
      return;
    }

    let forms = await awaitSafeQuery(`select sf_id from sitefotos_forms where sf_form_sharee_form_id in (27780, 30026,296599,328685)`);

    let otherForms = [296612,
      37943,
      296610,
      37939,
      324359,
      296603,
      296607]

    //add other forms to the list if not already exist
    for (let form of otherForms) {
      if (!forms.find(item => item.sf_id == form)) {
        forms.push({ sf_id: form });
      }
    }
    if (forms.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }
    console.log(forms)
    let buildings = await reportService.getHybridUserSites(internalUid);
    if (buildings.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    const siteIdMap = new Map();
    const siteIds = [];
    buildings.forEach(site => {
      const trades = site.mb_contract_type ? site.mb_contract_type.split(';') : [];
      const contractors = site.site_id ? site.site_id.split(',') : [];
      trades.forEach((trade, index) => {
        if (trade.includes('Janitorial services')) {
          const contractorIds = contractors[index].split(',');
          contractorIds.forEach(contractorId => {
            if (!siteIdMap.has(contractorId)) {
              siteIdMap.set(contractorId, site.site_id);
            }
            siteIds.push(contractorId);
          });
        }
      });
    });
    if (siteIds.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }
    const flatSiteIds = [...new Set(siteIds)];
    const start = moment().subtract(week * 7 + 1, 'days').startOf('week').unix();
    const end = moment().subtract(week * 7 - 6, 'days').startOf('week').unix();
    forms = forms.map(item => item.sf_id);

    let submissions = await mysql.awaitQuery(`select * from sitefotos_forms_submitted where sfs_form_id in (?) and sfs_building_id in (?) and sfs_created > ? and sfs_created < ?`, [forms, flatSiteIds, start, end]);

    let canonicalSubmissions = submissions.map(sub => siteIdMap.get(sub.sfs_building_id) || sub.sfs_building_id);
    let uniqueSubmissions = [...new Set(canonicalSubmissions)];

    let data = {
      buildings: siteIds.length,
      submissions: uniqueSubmissions.length
    };

    await redisClient.set(cacheKey, JSON.stringify(data), { 'EX': 1800 });
    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};
const getVerizonHVACData = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);


    const cacheKey = `getVerizonHVACData:${vendorId}:${internalUid}`;
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
       res.json(JSON.parse(cachedData));
       return;
    }

    let forms = await awaitSafeQuery(`select sf_id from sitefotos_forms where sf_form_sharee_form_id in (356861)`);
    if (forms.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    let buildings = await reportService.getHybridUserSites(internalUid);

    if (buildings.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    const siteIdMap = new Map();
    const siteIds = [];
    buildings.forEach(site => {
      const trades = site.mb_contract_type ? site.mb_contract_type.split(';') : [];
      const contractors = site.site_id ? site.site_id.split(',') : [];
      trades.forEach((trade, index) => {
        if (trade.includes('HVAC installation and maintenance')) {
          const contractorIds = contractors[index].split(',');
          contractorIds.forEach(contractorId => {
            if (!siteIdMap.has(contractorId)) {
              siteIdMap.set(contractorId, site.site_id);
            }
            siteIds.push(contractorId);
          });
        }
      });
    });
    if (siteIds.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    const flatSiteIds = [...new Set(siteIds)];


    // Start should be the start of the current quarter
    const start = moment().startOf('year').quarter(Math.ceil((moment().month() + 1) / 3)).startOf('quarter').unix();

    // End should be the end of the current quarter
    const end = moment().startOf('year').quarter(Math.ceil((moment().month() + 1) / 3)).endOf('quarter').unix();



    forms = forms.map(item => item.sf_id);


    let submissions = await mysql.awaitQuery(`select * from sitefotos_forms_submitted where sfs_form_id in (?) and sfs_building_id in (?) and sfs_created > ? and sfs_created < ?`, [forms, flatSiteIds, start, end]);

    let canonicalSubmissions = submissions.map(sub => siteIdMap.get(sub.sfs_building_id) || sub.sfs_building_id);
    let uniqueSubmissions = [...new Set(canonicalSubmissions)];

    let data = {
      buildings: siteIds.length,
      submissions: uniqueSubmissions.length
    };

    await redisClient.set(cacheKey, JSON.stringify(data), { 'EX': 1800 });
    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};
const getVerizonSpringCleanupData = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);


    const cacheKey = `getVerizonSpringCleanupData:${vendorId}:${internalUid}`;
    const cachedData = await redisClient.get(cacheKey);
    if (cachedData) {
      res.json(JSON.parse(cachedData));
      return;
    }

    let forms = await awaitSafeQuery(`select sf_id from sitefotos_forms where sf_form_sharee_form_id in (303419,36931)`);
    if (forms.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    let buildings = await reportService.getHybridUserSites(internalUid);
    if (buildings.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }

    const siteIdMap = new Map();
    const siteIds = [];
    buildings.forEach(site => {
      const trades = site.mb_contract_type ? site.mb_contract_type.split(';') : [];
      const contractors = site.site_id ? site.site_id.split(',') : [];
      trades.forEach((trade, index) => {
        if (trade.includes('Landscaping')) {
          const contractorIds = contractors[index].split(',');
          contractorIds.forEach(contractorId => {
            if (!siteIdMap.has(contractorId)) {
              siteIdMap.set(contractorId, site.site_id);
            }
            siteIds.push(contractorId);
          });
        }
      });
    });
    if (siteIds.length === 0) {
      return res.json({ buildings: 0, submissions: 0 });
    }
    const flatSiteIds = [...new Set(siteIds)];
    //as this is spring cleanup, therefore start should be 1st March and end should be 1st May this year
    const start = moment().startOf('year').month(2).date(1).unix();
    const end = moment().startOf('year').month(4).date(1).unix();


    forms = forms.map(item => item.sf_id);

    let submissions = await mysql.awaitQuery(`select * from sitefotos_forms_submitted where sfs_form_id in (?) and sfs_building_id in (?) and sfs_created > ? and sfs_created < ?`, [forms, flatSiteIds, start, end]);

    let canonicalSubmissions = submissions.map(sub => siteIdMap.get(sub.sfs_building_id) || sub.sfs_building_id);
    let uniqueSubmissions = [...new Set(canonicalSubmissions)];

    let data = {
      buildings: siteIds.length,
      submissions: uniqueSubmissions.length
    };

    await redisClient.set(cacheKey, JSON.stringify(data), { 'EX': 1800 });
    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};

const getWellsFargoPieReportRollup = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const week = req.query.week || 0;

    const cacheKey = `wellsFargoPieReportRollup:${week}`;
    const cachedData = await redisClient.get(cacheKey);

    if (cachedData) {
      res.json(JSON.parse(cachedData));
      return;
    }

    let forms = await awaitSafeQuery(`select sf_id from sitefotos_forms where sf_form_name = 'WF - Landscape Weekly Form' and sf_vendor_id in (11372,14913,14916,14914,14003,17877,18025,14902,14090,18026,14915,370,18027,14912,2054,11452,4486)`);

    if (forms.length == 0) {
      res.json([]);
      return;
    }

    let buildings = await mysql.awaitQuery(`select mb_id from sitefotos_site_client_mapping_extended_ungrouped left join maptile_building on mb_id=sscm_site_id where scs_client_vendor_id =15183 and FIND_IN_SET(mb_contract_type,'Landscaping')`);

    buildings = buildings.map(item => item.mb_id);

    if (buildings.length == 0) {
      res.json([]);
      return;
    }

    let start = moment().startOf('week').subtract(1, 'days').subtract(7 * week, 'days').format('X');
    let end = moment().startOf('week').subtract(1, 'days').subtract(7 * week, 'days').add(7, 'days').format('X');

    forms = forms.map(item => item.sf_id);

    let submissions = await mysql.awaitQuery(`select * from sitefotos_forms_submitted where sfs_form_id in (?) and sfs_building_id in (?) and sfs_created > ? and sfs_created < ?`, [forms, buildings, start, end]);

    let uniqueSubmissions = [...new Set(submissions.map(item => item.sfs_building_id))];

    let data = {
      buildings: buildings.length,
      submissions: uniqueSubmissions.length
    };

    await redisClient.set(cacheKey, JSON.stringify(data), { 'EX': 60 * 30 });

    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};

const getIssuesReport = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let dbDataPins = await mysql.awaitQuery(`SELECT mb_nickname, (select City from maptile_city where CityId = mb_city) as City, (select state_abv_name from maptile_state where id = mb_state ) as State, mb_zip_code as Zip, abc.photo, sfs_form_name, abc.mapPinURL, abc.latitude, abc.longitude, sfs.sfs_id, sfs.sfs_form_id, sfs.sfs_uploader_email, mvp.mvp_disc, mvp.mvp_dt FROM sitefotos_forms_submitted sfs LEFT JOIN maptile_building on sfs.sfs_building_id = mb_id, JSON_TABLE(sfs_form_data_full, '$.pages[*].elements[*]' COLUMNS ( id FOR ORDINALITY, TYPE VARCHAR(40) PATH "$.type", NESTED PATH '$.value[*]' COLUMNS ( photo VARCHAR(255) PATH '$.lrImageURL', mapPinURL VARCHAR(255) PATH '$.mapPinURL', latitude VARCHAR(255) PATH '$.lat', longitude VARCHAR(255) PATH '$.lon' ) ) ) abc LEFT JOIN maptile_vendor_pics mvp ON abc.photo = mvp.mvp_image and mvp_vendor_id=? WHERE sfs_form_id IN ( SELECT sf_id FROM sitefotos_forms WHERE sf_form_type = 'plotOnMap' AND sf_vendor_id = ? ) AND TYPE = 'file' AND photo IS NOT NULL;`, [vendorId, vendorId]);

    let data = [];
    for (let item of dbDataPins) {
      if (item.mapPinURL == null || item.mapPinURL == '') continue;
      //if mapPinURL does not contain ? then continue
      if (item.mapPinURL.indexOf('?') == -1) continue;
      //extract title from mapPinURL such as https://www.sitefotos.com/images/plotonmap/markeryellow.png?title=Curb
      let title = item.mapPinURL.split('?')[1].split('=')[1];
      let mapPinIcon = item.mapPinURL.split('?')[0];
      let object = {
        "Icon": mapPinIcon,
        "Site": item.mb_nickname,
        "City": item.City,
        "State": item.State,
        "Zip": item.Zip,
        "Photo": item.photo,
        "Issue": title,
        "Location": item.latitude + ',' + item.longitude,
        "Submission": item.sfs_id,
        "Form": item.sfs_form,
        "Uploader": item.sfs_uploader_email,
        "Description": item.mvp_disc,
        "DateTime": item.mvp_dt
      }
      data.push(object);
    }

    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex)
  }


}
const getWellsFargoSnowStormReport2 = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let buildings = req.body.buildings;

    buildings = buildings.map(item => parseInt(item));
    buildings = buildings.join(',');
    const allowedServiceTypes = [100, 101,103,104,114];

    //get snow storms data
    const snowStorms = await postgres.awaitQuery(/*SQL*/`SELECT wssr.*, wsm.*, ws.* as time_zone FROM weather_station_storms_readings wssr INNER JOIN weather_stations_mapping wsm ON wssr.wssr_ws_external_id = wsm.wsg_ws_external_id and wssr.wssr_provider_id = 101 INNER JOIN weather_stations ws ON wsm.wsg_ws_external_id = ws.ws_external_id  WHERE wsm.wsg_bid in (${buildings}) AND wssr.wssr_start_time > $1 AND wssr.wssr_end_time < $2`, [req.query.start, req.query.end]);
    if (snowStorms.length == 0) {
      res.json([]);
      return
    }
    //enumerate all the buildings with storm data
    let buildingIds = [...new Set(snowStorms.map(item => item.wsg_bid))];
    let buildingData = await mysql.awaitQuery(/*SQL*/`select mb_id, scs_client_internal_id, mb_nickname, mb_user_id, mb_zip_code, (select City from maptile_city where CityId = mb_city)
    as cityname, (select state_abv_name from maptile_state
    where id = mb_state ) as statename, mb_contract_type, mb_vendor_internal_id, mb_global_strategic_manager, mb_global_senior_manager, mb_global_regional_manager, mb_global_customer_location_id, mb_global_contract_type, ( select vendor_company_name from maptile_vendors where vendor_id = mb_user_id) as Service_Provider from maptile_building left join sitefotos_site_client_mapping_extended_ungrouped on mb_id=sscm_site_id where mb_id in (?)`, [buildingIds]);
    let photos = await mysql.awaitQuery(/*SQL*/`SELECT mvp_id, mvp_image, mvp_building_id,mvp_dt from maptile_vendor_pics left join maptile_building on mvp_building_id=mb_id where mvp_building_id in (?) and mvp_vendor_id=mb_user_id and mvp_dt BETWEEN ? and ?`, [buildingIds, req.query.start, req.query.end]);
    let serviceData = await mysql.awaitQuery(/*SQL*/`select swsd_id as service_id, mb_nickname as site_name, mb_id as site_id, swsd_service_name as service_name, vs_service_type_id, sst_service_type as serivce_type, svc.swsd_date_time as service_time, svc.swsd_form_submission_reference as submission_id, mb_address1 as address, ( select vendor_company_name from maptile_vendors where vendor_id = mb_user_id) as Service_Provider, mb_zip_code as zip, ( select City from maptile_city where CityId = mb_city) as city, ( select state_name from maptile_state where id = mb_state) as state, mb_vendor_internal_id as id from sitefotos_wp_services_data svc left join maptile_building on mb_id = svc.swsd_site_id left join vendor_services on vs_service_id = svc.swsd_service_id left join sitefotos_service_types on sst_id = vs_service_type_id where swsd_date_time>? and swsd_date_time<? and swsd_site_id in (?) and vs_service_type_id in (?) and find_in_set('Snow removal', mb_contract_type) group by swsd_id`, [req.query.start, req.query.end, buildingIds, allowedServiceTypes]);

    let data = [];
    for (let storm of snowStorms) {

      let building = buildingData.find(item => item.mb_id == storm.wsg_bid);
      const stormStart = new Date(storm.wssr_start_time * 1000).toLocaleString('en-US', { timeZone: storm.ws_timezone, year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: true });
      const stormEnd = new Date(storm.wssr_end_time * 1000).toLocaleString('en-US', { timeZone: storm.ws_timezone, year: 'numeric', month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit', hour12: true });
      if (!building) continue;
      const servicesDuringStorm = serviceData.filter(item => {
        const serviceTime = item.service_time;
        return item.site_id == storm.wsg_bid && serviceTime >= storm.wssr_start_time - 28800 && serviceTime <= storm.wssr_end_time + 28800
      });
      if (servicesDuringStorm.length > 0) continue;
      let photosDuringStorm = photos.filter(item => item.mvp_building_id == storm.wsg_bid && item.mvp_dt >= storm.wssr_start_time - 28800 && item.mvp_dt <= storm.wssr_end_time + 28800);
      if (photosDuringStorm.length == 0) continue;
      let object = {
        "Service_Provider": building.Service_Provider || '',
        "SiteID": storm.wsg_bid,
        "Site Name": building.scs_client_internal_id,
        "Trade(s)": building.mb_contract_type ? building.mb_contract_type.split(',') : [],
        "City": building.cityname,
        "State": building.statename,
        "Zip": building.mb_zip_code,
        "Start": stormStart,
        "End": stormEnd,
        "Storm ID": storm.wssr_storm_id,
        "Snow": storm.wssr_total,
        "Report": `https://fleet.sitefotos.com/wwstormreport?reportid=${storm.wssr_report_id}`,
        "Photos": photosDuringStorm.map(item => item.mvp_image)
      };
      data.push(object);
    }
    res.json(data);
  } catch (ex) {
    console.error(ex);
    next(ex)
  }




}

const getWellsFargoSnowStormChartReport = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let buildings = req.body.buildings;

    buildings = buildings.map(item => parseInt(item));
    buildings = buildings.join(',');
    const allowedServiceNames = ['Plow Lots', 'Shovel Walks', 'De-ice Lots', 'De-ice Walks', 'Snow Hauling'];

    // Get the start and end dates for the last 7 days
    const endDate = new Date();
    const startDate = new Date(endDate);
    startDate.setDate(startDate.getDate() - 7);

    // Get snow storms data for the last 7 days
    const snowStorms = await postgres.awaitQuery(/*SQL*/`
      SELECT wssr.*, wsm.*, ws.* as time_zone
      FROM weather_station_storms_readings wssr
      INNER JOIN weather_stations_mapping wsm ON wssr.wssr_ws_external_id = wsm.wsg_ws_external_id and wssr.wssr_provider_id = 101
      INNER JOIN weather_stations ws ON wsm.wsg_ws_external_id = ws.ws_external_id
      WHERE wsm.wsg_bid in (${buildings}) AND wssr.wssr_start_time > $1 AND wssr.wssr_end_time < $2
    `, [Math.round(startDate.getTime() / 1000), Math.round(endDate.getTime() / 1000)]);

    if (snowStorms.length === 0) {
      res.json([]);
      return;
    }

    // Get service data and photos for the last 7 days
    const serviceData = await mysql.awaitQuery(/*SQL*/`
      SELECT swsd_id as service_id, mb_nickname as site_name, mb_id as site_id, swsd_service_name as service_name, sst_service_type as service_type, svc.swsd_date_time as service_time
      FROM sitefotos_wp_services_data svc
      LEFT JOIN maptile_building on mb_id = svc.swsd_site_id
      LEFT JOIN vendor_services on vs_service_id = svc.swsd_service_id
      LEFT JOIN sitefotos_service_types on sst_id = vs_service_type_id
      WHERE swsd_date_time > ? AND swsd_date_time < ? AND swsd_site_id IN (${buildings}) AND vs_service_name IN (?) AND FIND_IN_SET('Snow removal', mb_contract_type)
      GROUP BY swsd_id
    `, [Math.round(startDate.getTime() / 1000), Math.round(endDate.getTime() / 1000), allowedServiceNames]);
    console.log(serviceData.length)

    // Create an array of dates for the last 7 days
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      dates.push(date.toLocaleString('en-US', { day: '2-digit', month: 'short' }));
    }

    // Initialize data arrays for snow events and sites serviced
    const snowEventsData = Array(7).fill(0);
    const sitesServicedData = Array(7).fill(0);

    // Count snow events and sites serviced for each day
    for (let storm of snowStorms) {
      const stormDate = new Date(storm.wssr_start_time * 1000);
      const index = Math.floor((stormDate - startDate) / (1000 * 60 * 60 * 24));
      snowEventsData[index]++;
    }

    // Create a Set to store unique combinations of site ID and date
    const servicedSites = new Set();

    for (let service of serviceData) {
      const serviceDate = new Date(service.service_time * 1000);
      const index = Math.floor((serviceDate - startDate) / (1000 * 60 * 60 * 24));
      const key = `${service.site_id}-${index}`;
      if (!servicedSites.has(key)) {
        sitesServicedData[index]++;
        servicedSites.add(key);
      }
    }



    // Prepare the data for Apex Charts
    const data = [
      {
        name: "Snow Events",
        data: snowEventsData,
        color: "#fdba8c",
      },
      {
        name: "Sites Serviced",
        data: sitesServicedData,
        color: "#1C64F2",
      },
    ];

    res.json({
      data: data,
      categories: dates,
    });
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};
const getWellsFargoSnowStormReport = async (req, res, next) => {
  try {
    console.time('getWellsFargoSnowStormReport');
    const { vendorId } = await login(req.cookies.JWT);
    const { start, end } = req.query;
    //added for mike to show service levels for demo

    let [serviceLevels] = await mysql.awaitQuery(/*SQL*/`select sesl_lot_pushes_range, sesl_lot_deices_range, sesl_walk_pushes_range, sesl_walk_deices_range  from sitefotos_estimator_service_levels where sesl_id = 9988`);

    const lotPushesRange = JSON.parse(serviceLevels.sesl_lot_pushes_range || "[]");
    const lotDeicesRange = JSON.parse(serviceLevels.sesl_lot_deices_range || "[]");
    const walkPushesRange = JSON.parse(serviceLevels.sesl_walk_pushes_range || "[]");
    const walkDeicesRange = JSON.parse(serviceLevels.sesl_walk_deices_range || "[]");

    const getSnowDepthIndex = (snowTotal) => {
      const snow = parseFloat(snowTotal) || 0;
      if (snow >= 12) return 12;
      return Math.floor(snow);
    };

    // 1. Get meta sites from snow vendor mapping
    console.time('getSnowVendorMapping');
    let mapping = await getSnowVendorMapping();
    console.timeEnd('getSnowVendorMapping');
    let uniqueVendors = [...new Set(mapping.map(item => item.vendor_id))];
    
    // 2. Get all building instances for these meta sites
    const buildingsQuery = `
      SELECT
        mb.mb_id,
        mb.mb_nickname,
        mb.mb_address1,
        sscm.sscm_site_id,
        sscm.scs_client_internal_id,
        sscm.scs_client_site_name,
        scsd.scsd_region,
        scsd.scsd_property_manager,
        scsd.scsd_senior_property_manager,
        scsd.scsd_regional_manager,
        scsd.scsd_location_type,
        (select City from maptile_city where CityId = mb_city) as cityname,
        (select state_abv_name from maptile_state where id = mb_state) as statename,
        mv.vendor_company_name as Service_Provider
      FROM maptile_building mb
      JOIN sitefotos_site_client_mapping_extended_ungrouped sscm ON mb.mb_id = sscm.sscm_site_id
      LEFT JOIN sitefotos_client_sites_data scsd ON sscm.sscm_client_site_id = scsd.scsd_client_site_id
      LEFT JOIN maptile_vendors mv ON mb.mb_user_id = mv.vendor_id
      WHERE sscm.scs_client_internal_id IN (?) and mb.mb_user_id in (?)`;

    console.time('buildingsQuery');
    const buildings = await mysql.awaitQuery(buildingsQuery, [mapping.map(m => m.id), uniqueVendors]);
    console.timeEnd('buildingsQuery');
    const buildingIds = buildings.map(b => b.mb_id);

    // 3. Get snow storms data
    console.time('snowStormsQuery');
    const snowStorms = await postgres.awaitQuery(
      `SELECT
        wssr.*,
        wsm.*,
        ws.*
      FROM weather_station_storms_readings wssr
      INNER JOIN weather_stations_mapping wsm
        ON wssr.wssr_ws_external_id = wsm.wsg_ws_external_id
        AND wssr.wssr_provider_id = 101
      INNER JOIN weather_stations ws
        ON wsm.wsg_ws_external_id = ws.ws_external_id
      WHERE wsm.wsg_bid = ANY($3)
        AND wssr.wssr_start_time > $1
        AND wssr.wssr_end_time < $2`,
      [start, end, buildingIds]
    );
    console.timeEnd('snowStormsQuery');

    

    // 4. Get service data
    const allowedServiceTypes = [100, 101, 103, 104, 114];
    const servicesQuery = `
      SELECT DISTINCT
        swsd_id as service_id,
        mb_id as site_id,
        swsd_service_name as service_name,
        vs_service_type_id,
        sst_service_type as service_type,
        swsd_date_time as service_time,
        swsd_form_submission_reference as submission_id,
        scs_client_internal_id as meta_site_id
      FROM sitefotos_wp_services_data svc
      LEFT JOIN maptile_building ON mb_id = swsd_site_id
      LEFT JOIN vendor_services ON vs_service_id = svc.swsd_service_id
      LEFT JOIN sitefotos_service_types ON sst_id = vs_service_type_id
      LEFT JOIN sitefotos_site_client_mapping_extended_ungrouped ON sscm_site_id = mb_id
      left join sitefotos_forms on sf_id = swsd_profile_id
      WHERE swsd_date_time > ?
        AND swsd_date_time < ?
        AND swsd_site_id IN (?)
        AND vs_service_type_id IN (?)
        AND sf_vendor_id IN (?)`;

    console.time('servicesQuery');
    const services = await mysql.awaitQuery(servicesQuery, [
      start,
      end,
      buildingIds,
      allowedServiceTypes,
      uniqueVendors
    ]);
    console.timeEnd('servicesQuery');

    // 5. Organize data by meta site
    console.time('dataProcessing');
    const buildingsMap = new Map(buildings.map(b => [b.mb_id, b]));
    const mappingMap = new Map(mapping.map(m => [m.id, m]));
    const servicesByMetaSite = services.reduce((acc, service) => {
      if (!acc[service.meta_site_id]) {
        acc[service.meta_site_id] = [];
      }
      acc[service.meta_site_id].push(service);
      return acc;
    }, {});
    //lets write it to a file
  
    const data = [];
    const processedStorms = new Set();
    for (const storm of snowStorms) {
      const building = buildingsMap.get(storm.wsg_bid);
      if (!building) continue;

      const mappingEntry = mappingMap.get(building.scs_client_internal_id);
      if (!mappingEntry) continue;

      const stormKey = `${storm.wssr_storm_id}-${building.scs_client_internal_id}`;
      if (processedStorms.has(stormKey)) continue;
      processedStorms.add(stormKey);

      const siteServices = servicesByMetaSite[building.scs_client_internal_id] || [];
      const servicesDuringStorm = siteServices.filter(svc => {
        return svc.service_time >= parseInt(storm.wssr_start_time) - 28800 &&
               svc.service_time <= parseInt(storm.wssr_end_time) + 28800;
      });

      const stormStart = new Date(storm.wssr_start_time * 1000)
        .toLocaleString('en-US', {
          timeZone: storm.time_zone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });

      const stormEnd = new Date(storm.wssr_end_time * 1000)
        .toLocaleString('en-US', {
          timeZone: storm.time_zone,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true
        });

      const snowIndex = getSnowDepthIndex(storm.wssr_total);
      data.push({
        "Service_Provider": mappingEntry.vendor,
        "SiteID": building.scs_client_internal_id,
        "Site Name": building.scs_client_site_name,
        "Region": building.scsd_region,
        "Property Manager": building.scsd_property_manager,
        "Senior Property Manager": building.scsd_senior_property_manager,
        "Regional Manager": building.scsd_regional_manager,
        "Location Type": building.scsd_location_type,
        "City": building.cityname,
        "State": building.statename,
        "Start": stormStart,
        "End": stormEnd,
        "Storm ID": storm.wssr_storm_id,
        "Snow": storm.wssr_total,
        "Report": `https://fleet.sitefotos.com/wwstormreport?reportid=${storm.wssr_report_id}`,
        "Plows": servicesDuringStorm.filter(s => s.vs_service_type_id === 101).length,
        "Expected Plows": lotPushesRange[snowIndex],
        "Shovels": servicesDuringStorm.filter(s => s.vs_service_type_id === 100).length,
        "Expected Shovels": walkPushesRange[snowIndex],
        "Deice Lots": servicesDuringStorm.filter(s => s.vs_service_type_id === 104).length,
        "Expected Deice Lots": lotDeicesRange[snowIndex],
        "Deice Walks": servicesDuringStorm.filter(s => s.vs_service_type_id === 103).length,
        "Expected Deice Walks": walkDeicesRange[snowIndex],
        "Snow Hauling": servicesDuringStorm.filter(s => s.vs_service_type_id === 114).length
      });
    }
    console.timeEnd('dataProcessing');

    res.json(data);
    console.timeEnd('getWellsFargoSnowStormReport');
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};
const getWellsFargoWeeklyLandscapeReport = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let buildings = req.body.buildings;
    const sheetData = await await getLandscapeVendorMapping();
    const allowedServiceNames = ['Mow/Trim/Edge/Blow', 'Weed/Bed Maintenance', 'Day Porter / Trash Policing']
    const services = await mysql.awaitQuery(/*SQL*/`SELECT swsd_id as service_id, mb_nickname as site_name, mb_id as site_id, swsd_service_name as service_name, sst_service_type as serivce_type, svc.swsd_date_time as service_time, svc.swsd_form_submission_reference as submission_id, mb_address1 as address, ( select vendor_company_name from maptile_vendors where vendor_id = mb_user_id) as Service_Provider, mb_zip_code as zip, ( select City from maptile_city where CityId = mb_city) as city, ( select state_name from maptile_state where id = mb_state) as state, mb_vendor_internal_id as id, scs_client_internal_id, scsd_region, scsd_property_manager, scsd_senior_property_manager, scsd_regional_manager, scsd_location_type, scsd_leased_owned, scsd_contract_type from sitefotos_wp_services_data svc left join maptile_building on mb_id = svc.swsd_site_id left join vendor_services on vs_service_id = svc.swsd_service_id left join sitefotos_service_types on sst_id = vs_service_type_id left join sitefotos_site_client_mapping_extended_ungrouped on sscm_site_id = mb_id left join sitefotos_client_sites_data on sscm_client_site_id = scsd_client_site_id where swsd_date_time>? and swsd_date_time<? and swsd_site_id in (?) and vs_service_name in (?) and find_in_set('Landscaping', mb_contract_type) group by swsd_id`, [req.query.start, req.query.end, buildings, allowedServiceNames]);
    
    const uniqueSubmissions = [...new Set(services.map(item => item.submission_id))];
    if (uniqueSubmissions.length == 0) {
      res.json({ services: [], photos: [] });
      return
    }
    const photos = await mysql.awaitQuery(/*SQL*/`SELECT photo,service, sfs_id, sfs_form_id, sfs_building_id from sitefotos_forms_submitted, JSON_TABLE(sfs_form_data_full, '$.pages[*].elements[*]'
      COLUMNS (
          service VARCHAR(255) PATH '$.ServiceName',
          NESTED PATH '$.elements[*]'
          COLUMNS (
              id FOR ORDINALITY,
              type VARCHAR(40) PATH '$.type',
              NESTED PATH '$.value[*]'
              COLUMNS (
                  photo VARCHAR(255) PATH '$.lrImageURL'
              )
          )
      )
  ) abc where sfs_id in (?) and type='file' and photo is not null`, [uniqueSubmissions]);
    res.json({ services, photos, sheetData });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}
const getServicesSnowReport = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const vendor = await mysql.awaitQuery('select * from maptile_vendors where vendor_id = ?', [vendorId]);
    if (vendor[0].vendor_hybrid == 'Y') {
      const result = await mysql.awaitQuery(/*SQL*/`select swsd_id as service_id,sesl_name as service_level, mb_nickname as site_name, mb_id as site_id, swsd_service_name as service_name, max(svc.swsd_date_time) as service_time, vs_service_type_id as service_type, count(vs_service_type_id) as service_count from sitefotos_wp_services_data svc left join maptile_building on mb_id = svc.swsd_site_id left join vendor_services on vs_service_id = svc.swsd_service_id left join sitefotos_pricing_contracts on spc_site_id=mb_id and spc_vendor_id=mb_hybrid_vendor_id and spc_contract_active='1' and spc_contract_for='CLIENT' left join sitefotos_estimator_service_levels on sesl_id=spc_sesl_id where mb_hybrid_vendor_id='?' and swsd_date_time>? and swsd_date_time<? group by vs_service_type_id, mb_id order by svc.swsd_date_time`, [vendorId, req.query.start, req.query.end])
      res.json(result);
    }
    else {
      const result = await mysql.awaitQuery(/*SQL*/`select swsd_id as service_id, sesl_name as service_level, mb_nickname as site_name, mb_id as site_id, swsd_service_name as service_name, max(svc.swsd_date_time) as service_time, vs_service_type_id as service_type, count(vs_service_type_id) as service_count from sitefotos_wp_services_data svc left join maptile_building on mb_id = svc.swsd_site_id left join vendor_services on vs_service_id = svc.swsd_service_id left join sitefotos_pricing_contracts on spc_site_id=mb_id and spc_vendor_id=mb_user_id and spc_contract_active='1' and spc_contract_for='CLIENT' left join sitefotos_estimator_service_levels on sesl_id=spc_sesl_id where swsd_vendor_id =? and swsd_date_time>? and swsd_date_time<? group by vs_service_type_id, mb_id order by svc.swsd_date_time`, [vendorId, req.query.start, req.query.end]);

      res.json(result);
    }
  }
  catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getSnowCommandCenterReport = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);

    // Existing query for services
    const services = await mysql.awaitQuery(/*SQL*/`
      select 
        swsd_id as service_id, 
        sesl_name as service_level, 
        mb_nickname as site_name, 
        mb_id as site_id, 
        swsd_service_name as service_name, 
        max(svc.swsd_date_time) as service_time, 
        vs_service_type_id as service_type, 
        count(vs_service_type_id) as service_count 
      from sitefotos_wp_services_data svc 
      left join maptile_building on mb_id = svc.swsd_site_id 
      left join vendor_services on vs_service_id = svc.swsd_service_id 
      left join sitefotos_pricing_contracts on spc_site_id=mb_id and spc_vendor_id=mb_user_id and spc_contract_active='1' and spc_contract_for='CLIENT' 
      left join sitefotos_estimator_service_levels on sesl_id=spc_sesl_id 
      where swsd_vendor_id = ? and swsd_date_time > ? and swsd_date_time < ? 
      group by vs_service_type_id, mb_id 
      order by svc.swsd_date_time
    `, [vendorId, req.query.start, req.query.end]);

    // New query for service levels with snow ranges
    const serviceLevels = await mysql.awaitQuery(/*SQL*/`
      SELECT 
        sesl_id,
        sesl_name,
        sesl_lot_pushes_range,
        sesl_lot_deices_range,
        sesl_walk_pushes_range,
        sesl_walk_deices_range
      FROM sitefotos_estimator_service_levels
      WHERE sesl_vendor_id = ? AND sesl_status = 'Active'
    `, [vendorId]);


    const snowRanges = [
      { min: 0, max: 0.99 },
      { min: 1, max: 1.99 },
      { min: 2, max: 2.99 },
      { min: 3, max: 3.99 },
      { min: 4, max: 4.99 },
      { min: 5, max: 5.99 },
      { min: 6, max: 6.99 },
      { min: 7, max: 7.99 },
      { min: 8, max: 8.99 },
      { min: 9, max: 9.99 },
      { min: 10, max: 10.99 },
      { min: 11, max: 11.99 },
      { min: 12, max: Infinity }
    ];


    const processedServiceLevels = serviceLevels.map(level => {
      const lotPushes = JSON.parse(level.sesl_lot_pushes_range);
      const lotDeices = JSON.parse(level.sesl_lot_deices_range);
      const walkPushes = JSON.parse(level.sesl_walk_pushes_range);
      const walkDeices = JSON.parse(level.sesl_walk_deices_range);

      return {
        id: level.sesl_id,
        name: level.sesl_name,
        requirements: snowRanges.map((range, index) => ({
          snowRange: range,
          lotPushes: lotPushes[index],
          lotDeices: lotDeices[index],
          walkPushes: walkPushes[index],
          walkDeices: walkDeices[index]
        }))
      };
    });


    const result = {
      services,
      serviceLevels: processedServiceLevels
    };

    res.json(result);
  } catch (ex) {
    console.error(ex);
    next(ex);
  }
};

const getTrueNorthReport1 = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    const data = await mysql.awaitSafeQuery(/*SQL*/`SELECT
      sfs_id as id,
      sfs_checkin AS CheckInTime,
      sfs_checkout AS CheckOutTime,
      mb_nickname AS SiteName,
      GROUP_CONCAT(swsd.swsd_service_name SEPARATOR ', ') AS Service,
      CASE WHEN sfs.sfs_uploader_vid = ? THEN 'SELF' ELSE 'SUB' END AS SELFSUB,
      IF(sfs.sfs_uploader_vid = ?, concat(vendor_fname, " ", vendor_lname), mv.vendor_company_name) AS Company,
      (select max(sps_price) from sitefotos_pricing_structure where sps_vendor_id = ? and sps_contract_for = 'CONTRACTOR' and sps_contract_active = '1' and sps_site_id = mb_id) as Payrate,
      (sfs_checkout-sfs_checkin) as TimeSpent,
      sfs_form_id as formid,
      sfs_form_name as formname,
      (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as Zone,
      mb_id as siteid,
      null as contactid,
      sfs_uploader_email as Email
    FROM sitefotos_forms_submitted sfs 
        JOIN maptile_building mb ON sfs.sfs_building_id = mb.mb_id 
        JOIN sitefotos_wp_services_data swsd ON sfs.sfs_id = swsd.swsd_form_submission_reference 
        LEFT JOIN maptile_vendors mv ON sfs.sfs_uploader_vid = mv.vendor_id 
    WHERE 
        sfs.sfs_checkin > ? AND sfs_checkout < ? AND sfs.sfs_vendor_id = ? AND sfs_checkin is not NULL AND sfs_checkout is not NULL GROUP BY sfs.sfs_id;`, [vendorId, vendorId, vendorId, req.query.start, req.query.end, vendorId]);

    const dataWithoutCheckout = await mysql.awaitSafeQuery(`select
      null as id,
      sss_checkin_time as CheckInTime,
      null as CheckOutTime,
      mb.mb_nickname AS SiteName,
      null as Service,
      null as SELFSUB,
      null as Company,
      null as Payrate,
      null as TimeSpent,
      sss_form_id as formid,
      sf_form_name as formname,
      (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as Zone,
      sss_site_id as siteid,
      sss_contact_id as contactid,
      sss_submitter_email as Email
    from
      sitefotos_sites_sessions
      left join sitefotos_forms on sss_form_id=sf_id
      left join maptile_building mb on mb.mb_id = sss_site_id
    where
      sss_vendor_id = ? and sss_checkin_time >= ? and sss_checkout_time is null order by sss_checkin_time desc`, [vendorId, req.query.start]);
    console.log(JSON.stringify(dataWithoutCheckout))
    res.json([...data, ...dataWithoutCheckout]);
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getPerOccuranceJobCostReport = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId || !req.query.start || !req.query.end) {
      console.error("Invalid Access Code or missing parameters");
      res.status(401).send('Unauthorized');
      return;
    }
    const sql = /*SQL*/`select
    distinct(swsd_site_id) as SITEID,
    (
    select
      mb_nickname
    from
      maptile_building
    where
      mb_id = SITEID) as SITE,
    (
      select
        (SELECT sgz_geo_zone from sitefotos_geofence_zones where sgz_zone_id = mb_zone_id) as mb_zone
      from
          maptile_building
      where
          mb_id = SITEID) as ZONE,
    swsd_service_name as SERVICE,
    (
    select
      CONCAT(sf_contact_fname , ' ', sf_contact_lname)
    from
      sitefotos_contacts
    where
      sf_contact_id = (
      select
        REPLACE(TRIM(']' FROM TRIM('[' FROM mb_contractor)), '\"', '')
      from
        maptile_building
      where
        mb_id = SITEID)) as Contractor,
    count(swsd_id) OVER(PARTITION BY swsd_site_id,
    swsd_service_id) as NUMBEROFSERVICES,
    (
    select
      max(sps_price)
    from
      sitefotos_pricing_structure
    where
      sps_vendor_id = ?
      and sps_contract_for = 'CONTRACTOR'
      and sps_contract_active = '1'
      and sps_site_id = SITEID)
  as PAYABLE,
    (count(swsd_id) OVER(PARTITION BY swsd_site_id,
    swsd_service_id)* (
    select
      max(sps_price)
    from
      sitefotos_pricing_structure
    where
      sps_vendor_id = ?
      and sps_contract_for = 'CONTRACTOR'
      and sps_contract_active = '1'
      and sps_site_id = SITEID)
           ) as TOTALPAYABLE,
    (
    select
      CONCAT(sf_contact_fname , ' ', sf_contact_lname)
    from
      sitefotos_contacts
    where
      sf_contact_id =(
      select
        mb_client
      from
        maptile_building
      where
        mb_id = SITEID)) as Customer,
    (
    select
      max(sps_price)
    from
      sitefotos_pricing_structure sps
    where
      sps_vendor_id = ?
      and sps_contract_for = 'CLIENT'
      and sps_contract_active = '1'
      and sps.sps_site_id = SITEID)
  as BILLABLE,
    (count(swsd_id) OVER(PARTITION BY swsd_site_id,
    swsd_service_id) * (
    select
      max(sps_price)
    from
      sitefotos_pricing_structure sps
    where
      sps_vendor_id = ?
      and sps_contract_for = 'CLIENT'
      and sps_contract_active = '1'
      and sps_site_id = SITEID)
  ) as TOTALBILLABLE,
    ( ((
      count(swsd_id) OVER(PARTITION BY swsd_site_id,
    swsd_service_id)) * (
    select
      max(sps_price)
    from
      sitefotos_pricing_structure sps
    where
      sps_vendor_id = ?
      and sps_contract_for = 'CLIENT'
      and sps_contract_active = '1'
      and sps_site_id = SITEID)
  ) -
  ((
      count(swsd_id) OVER(PARTITION BY swsd_site_id,
    swsd_service_id)) * (
    select
      max(sps_price)
    from
      sitefotos_pricing_structure sps
    where
      sps_vendor_id = ?
      and sps_contract_for = 'CONTRACTOR'
      and sps_contract_active = '1'
      and sps_site_id = SITEID)
           )
  ) as MARGIN
  from
    sitefotos_wp_services_data
  where
    swsd_site_id in (
    select
      sps_site_id
    from
      sitefotos_pricing_structure
    where
      sps_site_id in(
      select
        sps_site_id
      from
        sitefotos_pricing_structure
      where
        sps_site_id in (
        select
          swsd_site_id
        from
          sitefotos_wp_services_data
        where
          swsd_vendor_id = ?
          and swsd_date_time > ?
          and swsd_date_time < ?
        group by
          swsd_site_id
   )
        and sps_contract_active = '1'
        and sps_contract_for = 'CONTRACTOR')
      and sps_contract_active = '1'
      and sps_contract_for = 'CLIENT')
    and swsd_vendor_id = ?
    and swsd_date_time > ?
    and swsd_date_time < ?
  `;
    const result = await mysql.awaitQuery(sql, [vendorId, vendorId, vendorId, vendorId, vendorId, vendorId, vendorId, req.query.start, req.query.end, vendorId, req.query.start, req.query.end]);
    res.json(result.map((item, index) => ({ ...item, rowNumber: index + 1 })));
  } catch (error) {
    console.log(error);
    next(error)
  }
}

const getPricingReport = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    //We first need to get contract details from pricing structure for this site and vendor
    const getPricingDetails = `
          select *
          from sitefotos_pricing_structure
          where
              sps_vendor_id = ? and
              sps_site_id = ? and sps_contract_active = '1' and sps_contract_for = ?
      `;
    const getPricingSql = `
          select
              swsd_id as service_id,
              mb_nickname as site_name,
              mb_id as site_id,
              swsd_service_name as service_name,
              from_unixtime(svc.swsd_date_time) as time,
              swsd_snow_inch as snow_inch,
              swsd_hours as hours,
              swsd_service_id as s_id,
              swsd_people as people,
              vs_service_type as connected_service,
              vs_equipment_id as equipment_id,
              vs_service_options as service_options_vendor_services,
              vs_service_type_id as service_type_id,
              swsd_service_options as options
          from sitefotos_wp_services_data svc
              inner join maptile_building
                  on mb_id=svc.swsd_site_id
              inner join vendor_services
                  on vs_service_id = svc.swsd_service_id
          where swsd_vendor_id=?
            and swsd_site_id=?
            and swsd_date_time >= ?
            and swsd_date_time < ?
          order by svc.swsd_date_time
      `;
    const pricingDetails = await mysql.awaitQuery(getPricingDetails, [vendorId, req.body.siteId, req.body.contractFor]);
    const lineItems = await mysql.awaitQuery(getPricingSql, [vendorId, req.body.siteId, req.body.start, req.body.end]);
    res.json({
      'pricingDetails': pricingDetails,
      'lineItems': lineItems
    });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
};

const getContractorData = (contractors, id) => {
  if (contractors !== undefined) {
    const target = contractors.find(y => y.sf_contact_id === id);

    if (target) {
      const contractorName = target.sf_contact_company_name ?
        target.sf_contact_company_name :
        `${target.sf_contact_fname} ${target.sf_contact_lname}`;

      return {
        CONTRACTORROW: [target],
        CONTRACTORIDS: [target.sf_contact_id],
        CONTRACTORNAMES: [contractorName],
        CONTRACTOREMAILS: [target.sf_contact_email],
      };
    }
  }

  return {
    CONTRACTORROW: [],
    CONTRACTORIDS: [],
    CONTRACTORNAMES: [],
    CONTRACTOREMAILS: []
  };

};

const handleContractors = (contractors, item) => {
  if (item.contractors) {
    let con = item.contractors.split(',');
    const contractorData = con.map(id => getContractorData(contractors, id));
    item = { ...item, ...contractorData };
  } else {
    item = { ...item, ...getContractorData() };
  }
  return item;
};

const getPricingReportContractor = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    //We first need to get contract details from pricing structure for this site and vendor
    const getPricingDetails = `
          select *
          from sitefotos_pricing_structure
          where
              sps_vendor_id = ? and sps_contract_active = '1' and sps_contract_for = ?
      `;
    const getContractorDetailsFromVendorTable = `
            select mv.vendor_id from maptile_vendors as mv
                inner join sitefotos_contacts as sf on mv.vendor_access_code = sf.sf_contact_contractorid
                where sf.sf_contact_id = ?`;
    const getPricingSql = `
          select
              swsd_id as service_id,
              mb_nickname as site_name,
              mb_id as site_id,
              swsd_service_name as service_name,
              from_unixtime(svc.swsd_date_time) as time,
              swsd_snow_inch as snow_inch,
              swsd_hours as hours,
              swsd_people as people,
              swsd_service_id as s_id,
              vs_service_type as connected_service,
              vs_equipment_id as equipment_id,
              vs_service_options as service_options_vendor_services,
              swsd_service_options as options
          from sitefotos_wp_services_data svc
              inner join maptile_building
                  on mb_id=svc.swsd_site_id
              inner join vendor_services
                  on vs_service_id = svc.swsd_service_id
          where swsd_vendor_id=?
            and swsd_uploader_vid = ?
            and swsd_date_time >= ?
            and swsd_date_time < ?
          order by svc.swsd_date_time
      `;

    const userDetails = await mysql.awaitQuery(getContractorDetailsFromVendorTable, [req.body.uploaderVid]);
    const pricingDetails = await mysql.awaitQuery(getPricingDetails, [vendorId, req.body.contractFor]);
    let lineItems = [];
    if (userDetails.length > 0) {
      lineItems = await mysql.awaitQuery(getPricingSql, [
        vendorId,
        userDetails[0].vendor_id,
        req.body.start,
        req.body.end
      ]);
    }

    res.json({
      'pricingDetails': pricingDetails,
      'lineItems': lineItems
    });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
};

const getPricingReportClient = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    //We first need to get contract details from pricing structure for this site and vendor
    const getPricingDetails = `
          select *
          from sitefotos_pricing_structure
          where
              sps_vendor_id = ? and sps_contract_active = '1' and sps_contract_for = ?
      `;
    const getPricingSql = `
          select
              swsd_id as service_id,
              mb_nickname as site_name,
              mb_id as site_id,
              swsd_service_name as service_name,
              from_unixtime(svc.swsd_date_time) as time,
              swsd_snow_inch as snow_inch,
              swsd_hours as hours,
              swsd_people as people,
              swsd_service_id as s_id,
              vs_service_type as connected_service,
              vs_equipment_id as equipment_id,
              vs_service_options as service_options_vendor_services,
              swsd_service_options as options
          from sitefotos_wp_services_data svc
              inner join maptile_building
                  on mb_id=svc.swsd_site_id
              inner join vendor_services
                  on vs_service_id = svc.swsd_service_id
          where swsd_vendor_id=?
            and mb_client = ?
            and swsd_date_time >= ?
            and swsd_date_time < ?
          order by svc.swsd_date_time
      `;

    const pricingDetails = await mysql.awaitQuery(getPricingDetails, [vendorId, req.body.contractFor]);
    const lineItems = await mysql.awaitQuery(getPricingSql, [vendorId, req.body.client_id, req.body.start, req.body.end]);
    res.json({
      'pricingDetails': pricingDetails,
      'lineItems': lineItems
    });
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
};

const getWeeklyTimeSheet = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    let start = req.query.start;
    let end = req.query.end;
    let worker = req.query.worker;

    let ClockTime = await mysql.awaitQuery(/*SQL*/`SELECT   CONCAT(sc.sf_contact_fname," ",sc.sf_contact_lname) as Employee, sbt.sbt_name as Event, sbd.sbd_logged as Time  from sitefotos_breadcrumb_data sbd  inner join sitefotos_breadcrumb_types sbt on sbt.sbt_type = sbd.sbd_type  inner join sitefotos_contacts sc on sc.sf_contact_email = sbd.sbd_email  and sc.sf_contact_vendorid = sbd.sbd_vendor_id and sc.sf_contact_type = 'EMPLOYEE'   inner join maptile_building mb on mb.mb_id = sbd.sbd_nearest_building_id where sbd.sbd_vendor_id = ? and sbd_logged > ? and sbd_logged <= ?  and sbt_type in(4,5) and sf_contact_id = ? order by  Employee asc, Time asc`, [vendorId, start, end, worker]);

    let SiteTime = await mysql.awaitQuery(/*SQL*/`SELECT  CONCAT(sc.sf_contact_fname," ",sc.sf_contact_lname) as Employee  ,mb.mb_nickname as Site, sfs.sfs_checkin as CheckIn, sfs.sfs_checkout as CheckOut, ((sfs.sfs_checkout - sfs.sfs_checkin)/60) as TotalTimeInMinutes, swsd_service_name as Task  from sitefotos_wp_services_data swsd   inner join sitefotos_forms_submitted sfs on sfs.sfs_id = swsd.swsd_form_submission_reference  inner join maptile_building mb on mb.mb_id = swsd.swsd_site_id inner join sitefotos_contacts sc on sc.sf_contact_email = swsd.swsd_email and sc.sf_contact_vendorid = swsd.swsd_vendor_id and sc.sf_contact_type = 'EMPLOYEE' AND swsd_vendor_id= ? AND swsd_date_time < ? AND swsd_date_time >= ?  AND swsd_active=1 and sc.sf_contact_id=?`, [vendorId, end, start, worker]);

    res.status(200).json({
      ClockTime,
      SiteTime
    });

  }
  catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getContractorDetails = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    const getContractorDetails = `
        select * from maptile_vendors where vendor_access_code in (?) and vendor_id = ?
      `;
    const contractorDetails = await mysql.awaitQuery(getContractorDetails, [req.body.ids, vendorId]);
    res.json({
      'contractorDetails': contractorDetails
    });
  }
  catch (ex) {
    console.log(ex.message)
    next(ex)
  }
}
const siteSessionsServices = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    const getSiteSessionsServices = await mysql.awaitQuery(/*SQL*/`SELECT sfs_id as id, sfs_checkin AS CheckInTime, sfs_checkout AS CheckOutTime, mb_nickname AS SiteName, GROUP_CONCAT(swsd.swsd_service_name SEPARATOR ', ') AS Service, (sfs_checkout-sfs_checkin) as TimeSpent FROM sitefotos_forms_submitted sfs JOIN maptile_building mb ON sfs.sfs_building_id = mb.mb_id JOIN sitefotos_wp_services_data swsd ON sfs.sfs_id = swsd.swsd_form_submission_reference LEFT JOIN maptile_vendors mv ON sfs.sfs_uploader_vid = mv.vendor_id WHERE sfs.sfs_checkin > ? AND sfs_checkout < ? AND sfs.sfs_vendor_id = ? AND sfs_checkout is NOT NULL AND sfs_checkin is NOT NULL GROUP BY sfs.sfs_id;`, [req.query.start, req.query.end, vendorId]);
    res.status(200).json(getSiteSessionsServices);
  }
  catch (ex) {
    console.log(ex.message || ex)
    next(ex)
  }
}

const siteSessions = async (req, res, next) => {
  try {
    const { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    const getSiteSessions = await mysql.awaitQuery(/*SQL*/`SELECT sfs_id, sfs_checkin AS StartTime, sfs_checkout AS StopTime, (sfs_checkout-sfs_checkin) as TimeSpent, mb_id as SiteID, mb_nickname as SiteName, sfs_uploader_email as WorkerEmail from sitefotos_forms_submitted  LEFT JOIN maptile_building ON sfs_building_id = mb_id WHERE sfs_checkin > ? AND sfs_checkout < ? AND sfs_vendor_id = ? AND sfs_checkout is NOT NULL AND sfs_checkin is NOT NULL AND sfs_checkin <> 0 AND sfs_checkout <> 0;`, [req.query.start, req.query.end, vendorId]);
    res.status(200).json(getSiteSessions);
  }
  catch (ex) {
    console.log(ex.message || ex)
    next(ex)
  }
}
const checkinsWithoutCheckout = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT)
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }
    const sql = `select
      sss_form_id as formid,
      sf_form_name as formname,
      sss_site_id as siteid,
      sss_checkin_time as checkintime,
      sss_contact_id as contactid,
      sss_submitter_email as email
    from
      sitefotos_sites_sessions
      left join sitefotos_forms on sss_form_id=sf_id
    where
      (sss_vendor_id = ? or sss_uploader_vid = ?)
      and sss_checkin_time >= ? and sss_checkout_time is null order by sss_checkin_time desc`
    const results = await mysql.awaitQuery(sql, [vendorId, vendorId, req.query.start]);
    res.json(results)
  } catch (ex) {
    console.error(ex);
    next(ex)
  }
}

const getFormsByType = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT)
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }

    let forms = await service.getSubmittedFormsByType(req.query.fid, vendorId, req.query.start, req.query.end)
    let resp = []
    for (let form of forms) {
      if (form['Date'] > 1542349800 && form['Data'].trim()) {
        delete form['Data'];
        resp.push(form)
      }
    }
    if (!resp) {
      res.status(404).json({ 'error': 'Form not found' });
      return
    }

    res.status(200).json(resp);
  } catch (error) {
    next(error)
  }
}

const getFormToTable = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT)
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }

    let formResp = await service.getSubmittedForm(req.query.sfid, vendorId)
    if (!formResp) {
      res.status(404).json({ 'error': 'Form not found' });
      return
    }
    let resp = null
    let form = formResp[0]
    if (form['Date'] > 1542349800 && form['sfs_form_data_full'].trim())
      resp = await service.getTableRowForForm(JSON.parse(form['sfs_form_data_full']))
    res.status(200).json(resp);
  } catch (error) {
    next(error)
  }

}

const getReportEmail = async (req, res, next) => {
  try {
    let vendorId = 0;
    if (req.cookies.JWT != undefined) {
      vendorId = await login(req.cookies.JWT);
    }

    let queryString = req.body.query_string;

    if (!queryString) {
      res.json({ success: 0, message: "missing params" });
      return
    }

    let record = await mysql.awaitQuery('select * from sitefotos_postdated_services where sps_querystring=CAST(? AS BINARY)', [queryString]);

    for (let recordElement of record) {
      let servicesIds = recordElement.sps_swsd_id;
      const values = servicesIds.join(',');
      const services = await mysql.awaitSafeQuery(
        `SELECT swsd.*, sfs.sfs_checkin, sfs.sfs_checkout, CONCAT( ROUND(swmd.swmd_mat_usage, 0) , ' ', swmd.swmd_mat_unit) as material
               FROM sitefotos_wp_services_data swsd
               LEFT JOIN sitefotos_forms_submitted sfs on sfs.sfs_id = swsd.swsd_form_submission_reference
               LEFT JOIN (SELECT swmd_parent_service_submission_id, MIN(swmd_mat_id),swmd_mat_usage,swmd_mat_unit 
                          FROM sitefotos_wp_material_data
                          GROUP BY swmd_parent_service_submission_id) swmd
                            ON swmd.swmd_parent_service_submission_id = swsd.swsd_id
                          WHERE swsd.swsd_id IN (${values}) and swsd.swsd_active = 1`
      );
      recordElement.services_fromwp = services;
    }

    if (record.length == 0) {
      res.json({ success: 0, message: "data not found" });
      return
    }

    let vendor = await mysql.awaitQuery('select *  from maptile_vendors where vendor_id =?', [record[0].sps_vendor_id]);

    let contractor = await mysql.awaitSafeQuery('select CONCAT(sc.sf_contact_fname , " ", sc.sf_contact_lname) as contractornamefromcontacttable,sc.sf_contact_email as contractoremailfromcontacttable, sc.sf_contact_id as contactid, mv.vendor_id as vendoridfrommaptiletable,mv.vendor_email, CONCAT(vendor_fname, " ", vendor_lname) as completename, mv.vendor_company_name as companyname,mv.vendor_access_code as vendor_access_code from maptile_vendors mv join sitefotos_contacts sc on mv.vendor_access_code = sc.sf_contact_contractorid where mv.vendor_id =?;', [record[0].sps_contractor_id])

    //We also need buildings for this particular client so lets fetch them here as well
    let buildings = await mysql.awaitQuery('select *, (select City from maptile_city where CityId = mb.mb_city) as cityname, (select state_name from maptile_state where id = mb.mb_state) as statename from maptile_building mb where mb.mb_user_id =? and JSON_CONTAINS(mb_contractor_vid, JSON_QUOTE("?")) and mb.mb_status ="1" ;', [record[0].sps_vendor_id, record[0].sps_contractor_id])

    res.status(200).json(
      {
        success: 1,
        message: record,
        buildings: buildings,
        loggedinuser: vendorId,
        vendorDetails: vendor,
        contractorDetails: contractor
      })
  } catch (error) {
    next(error)
  }
}

const updateReportEmail = async (req, res, next) => {
  try {
    let vendorId = req.body.vid;
    let queryString = req.body.query_string;
    let rawData = req.body.rawData;
    let status = req.body.status; //If status 2 insert new rows, if status 3 update the rows.

    if (!queryString) {
      res.json({ success: 0, message: "missing params" });
      return
    }
    let insertedRows = [];
    for (const data of rawData) {
      if (status == 2) {
        let inserted =
          await mysql.insertObj('sitefotos_postdated_services', data);
        insertedRows.push({
          rowid: inserted.id,
          rowdata: data
        });
      }
      else if (status == 3) {
        console.log("data", data)
        let updated =
          await mysql.updateObj(
            'sitefotos_postdated_services',
            {
              'sps_status': data.sps_status,
              'sre_form_submitted_id': 0
            },
            ['sre_id'],
            [data.sre_id]
          );
        insertedRows.push({
          rowid: 0,
          rowdata: data
        });
      }
    }

    let singleRow = await awaitSafeQuery(`select * from sitefotos_postdated_services where sps_querystring=?`, [queryString]);


    await mysql.updateObj(
      'sitefotos_postdated_services',
      {
        sre_contractor_name: singleRow[0].sre_contractor_name,
        sps_report_status: status,
        sps_report_start_date: singleRow[0].sps_report_start_date,
        sre_client_id: singleRow[0].sre_client_id,
        sre_client_name: singleRow[0].sre_client_name
      },
      ['sps_querystring'],
      [queryString]
    );

    //Now that new services have been inserted we need to update rows statuses and also add items to columns that are empty.


    // let updateObj = {
    //   sre_data_json: JSON.stringify(rawData),
    //   sps_report_status: status,
    //   sre_form_ids: JSON.stringify(formsThatAreSubmitted)
    // };
    // let updateRecord = await mysql.updateObj('sitefotos_postdated_services', updateObj, ['sps_vendor_id','sps_querystring'], [vendorId,queryString]);
    //
    // if (updateRecord.length == 0){
    //   res.json({success: 0, message: "update failed"});
    //   return
    // }

    res.status(200).json({ success: 1 })
  } catch (error) {
    next(error)
  }
}

const getServiceDetails = async (req, res, next) => {
  try {
    let vendorId = req.body.vid;
    let services = req.body.services;
    let servicesConverted = `"${services.join('","')}"`;

    if (!services) {
      res.json({ success: 0, message: "missing params" });
      return
    }

    let record = await mysql.awaitQuery(`select * from vendor_services where vs_service_id in (${servicesConverted}) and vs_vendor_id=?`, [vendorId]);

    if (record.length == 0) {
      res.json({ success: 0, message: "data not found" });
      return
    }

    res.status(200).json({ success: 1, message: record })
  } catch (error) {
    next(error)
  }
}

const reportAllContractors = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT)
    if (!vendorId) {
      res.status(401).send(err.message);
      return
    }
    let startDate = req.query.startdate;
    let endDate = req.query.enddate;
    let perEventCalculation = {
      snow: 1,
      totalPrice: 0,
      pushType: null,
      pushWalkPrice: 0,
      pushLotPrice: 0,
      deiceLotPrice: 0,
      deiceWalkPrice: 0,
      deicePublicWalkPrice: 0,
    }
    let pricingStructure = await reportService.getPricingStructure(vendorId, 'CONTRACTOR');
    let lineItems = await reportService.getPricingForAllContractors(vendorId, startDate, endDate);

    let eventPushWalksFirstrow = false;
    let eventPushLotsFirstrow = false;
    let eventPushPublicWalksFirstrow = false;

    let pricingResults = lineItems.map(svc => {
      let connected = pricingStructure.find(e => e.sps_service_type === svc.connected_service || e.sps_service_type === svc.service_name);

      if (typeof (connected) != "undefined") {
        const now = new Date();
        svc.date = moment(svc.time).format("MM/DD/YYYY");
        svc.time = moment(svc.time).format("MM/DD/YYYY hh:mm a");
        // svc.date = dateFns.format(Date.parse(svc.time), "MM/DD/YYYY");
        // svc.time = dateFns.format(Date.parse(svc.time), "MM/DD/YYYY hh:mm a");

        svc.desc = svc.time;
        svc.invoice_num = 1001;
        svc.invoice_date = moment(new Date()).format("MM/DD/YYYY");
        svc.due_date = new Date(now.setMonth(now.getMonth() + 1));
        svc.contract_type = connected.sps_contract_type;
        svc.service_cat = connected.sps_service_type;
        svc.quantity = 1;
        svc.hours = svc.hours === null ? 1 : svc.hours;
        svc.people = svc.people === null ? 1 : svc.people;

        if (connected.sps_service_option_trigger === 'tm') {
          svc.price = connected.sps_price;
          //It is time and materials which means multiply hours with price
          if (svc.service_cat.startsWith('PUSH')) {
            svc.total = svc.hours * svc.people * connected.sps_price;
          }
          else {
            //Deicing
            svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price)
          }
        }

        else if (connected.sps_service_option_trigger === 'po') {
          svc.price = connected.sps_price;
          svc.total = connected.sps_price;
        }

        else if (connected.sps_service_option_trigger === 'pppa') {
          svc.price = connected.sps_price;
          if (svc.service_cat.startsWith('PUSH')) {
            //Now here price will be on threshold

            svc.snow_inch = reportService.getSnowDataOptions(svc.options)
            //Lets see in which category does snow fall in

            let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
            if (typeof connectedSnow != 'undefined') {
              //Now due to hybrid contracts we also have to check if selected pricing should come from equipment rate
              if (svc.equipment_id > 0) {
                let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                if (connectedTandMRow != undefined) {
                  //Since this is t and m, we need to apply other stuff
                  svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                }
              } else {
                svc.total = connectedSnow.sps_price
              }
            }
            else {
              //Here we need to check if it falls under 12+ or time and materials
              connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
              if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                svc.total = svc.snow_inch * connectedSnow.sps_price;
              } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                //Tandm rates will apply
                //Now here we have to check the equipment used and multiply its price
                let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
              }
              else {
                //If user has selected time and material rates will apply, we need to code that
                svc.total = 0
              }
            }
          }
          else {
            //Deicing
            svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price)
          }
        }

        else if (connected.sps_service_option_trigger === 'pepipa') {

          if (svc.service_cat.startsWith('PUSH')) {
            if (connected.sps_service_type === 'PUSH_LOTS') {
              // svc.total = connected.sps_price;
              svc.snow_inch = perEventCalculation.snow;

              if (!eventPushLotsFirstrow) {
                let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
                if (typeof connectedSnow != 'undefined') {
                  svc.total = connectedSnow.sps_price
                }
                else {
                  connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                  if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                    svc.total = svc.snow_inch * connectedSnow.sps_price;
                  } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                    //Tandm rates will apply
                    //Now here we have to check the equipment used and multiply its price
                    let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                    svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                  }
                  else {
                    //If user has selected time and material rates will apply, we need to code that
                    svc.total = 0
                  }
                }
                eventPushLotsFirstrow = true;
              }

            }
            if (connected.sps_service_type === 'PUSH_WALKS') {
              // svc.total = connected.sps_price;
              svc.snow_inch = perEventCalculation.snow;
              if (!eventPushWalksFirstrow) {
                let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
                if (typeof connectedSnow != 'undefined')
                  svc.total = connectedSnow.sps_price
                else {
                  connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                  if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                    svc.total = svc.snow_inch * connectedSnow.sps_price;
                  } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                    //Tandm rates will apply
                    //Now here we have to check the equipment used and multiply its price
                    let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                    svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                  }
                  else {
                    //If user has selected time and material rates will apply, we need to code that
                    svc.total = 0
                  }
                }
                eventPushWalksFirstrow = true;
              }

            }
            if (connected.sps_service_type === 'PUSH_PUBLIC_WALKS') {
              // svc.total = connected.sps_price;
              svc.snow_inch = perEventCalculation.snow;
              if (!eventPushPublicWalksFirstrow) {
                let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
                if (typeof connectedSnow != 'undefined')
                  svc.total = connectedSnow.sps_price
                else {
                  connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                  if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                    svc.total = svc.snow_inch * connectedSnow.sps_price;
                  } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                    //Tandm rates will apply
                    //Now here we have to check the equipment used and multiply its price
                    let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                    svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                  }
                  else {
                    //If user has selected time and material rates will apply, we need to code that
                    svc.total = 0
                  }
                }
                eventPushPublicWalksFirstrow = true;
              }
            }
          }
          else {
            //Deicing
            svc.price = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
            svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);

            if (connected.sps_service_type === 'DEICE_LOTS') {
              perEventCalculation.deiceLotPrice += reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
            }
            if (connected.sps_service_type === 'DEICE_WALKS') {
              perEventCalculation.deiceWalkPrice += reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
            }
            if (connected.sps_service_type === 'DEICE_PUBLIC_WALKS') {
              perEventCalculation.deicePublicWalkPrice += reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
            }
          }
        }

        else if (connected.sps_service_option_trigger === 'su') {
          // svc.total = connected.sps_price;
          svc.total = 0;
        }

        else if (connected.sps_service_option_trigger === 'sh') {
          if (svc.service_cat.startsWith('DEICE')) {
            svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price)
          } else {
            svc.total = 0;
          }
        }
        return svc;
      }
    });

    pricingResults = pricingResults.filter(s => s != null);
    pricingResults = pricingResults.filter(s => s.contractor_vid == 178); //This needs to be every contractor on the report

    const vars = {
      firstName: 'Mike',
      data: pricingResults
    }

    let emailResp = await sendEmailTemplate(
      'Amad',
      '<EMAIL>',
      'Contractor Work Summary from ' + senderVendorDetails[0].companyname,
      './static/emails/reports/batchofbatchcontractoremail.html',
      vars,
      '<EMAIL>'
    )
    console.log("postdatedserviceemail", emailResp)

    res.status(200).json(pricingResults);
  } catch (error) {
    next(error)
  }
}

const sendBatchReportInEmail = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      console.error("Invalid Access Code");
      res.status(401).send(err.message);
      return
    }

    let perEventCalculation = {
      snow: 1,
      totalPrice: 0,
      pushType: null,
      pushWalkPrice: 0,
      pushLotPrice: 0,
      deiceLotPrice: 0,
      deiceWalkPrice: 0,
      deicePublicWalkPrice: 0,
    }

    let eventPushWalksFirstrow = false;
    let eventPushLotsFirstrow = false;
    let eventPushPublicWalksFirstrow = false;

    //First we need to get start date and end date in unix
    let startDate = req.body.startdate;
    let endDate = req.body.enddate;

    //Now we need to get all the pricing data
    let pricingStructure = await reportService.getPricingStructure(vendorId, 'CONTRACTOR');
    let lineItems = await reportService.getPricingForAllContractors(vendorId, startDate, endDate);

    //Now we need to create array with all the contractors that are in the line items
    let contractors = lineItems.map(li => {
      let newObj = {
        id: li.contractor_vid,
        email: li.contractor_email,
        name: li.contractor_name,
        companyName: li.company_name,
        logo: li.company_logo
      }
      return newObj
    });

    //Remove dups
    const ids = contractors.map(o => o.id)
    contractors = contractors.filter(({ id }, index) => !ids.includes(id, index + 1));

    //Now we loop over each contractor, then we find all the sites from lineItems for each of the contractor and then we go through our logic for it
    for (let contractor of contractors) {
      let cid = contractor.id;
      let contractorEmail = contractor.email;
      let contractorName = contractor.name;
      let companyName = contractor.companyName;
      let contractorLogo = contractor.logo;

      //Lets find sender details
      let senderVendorDetails = await mysql.awaitQuery(`select mv.vendor_email, CONCAT(vendor_fname, " ", vendor_lname) as completename, mv.vendor_company_name as companyname, mv.vendor_company_logo as company_logo from maptile_vendors mv where mv.vendor_id =?`, [vendorId]);
      let rawData = lineItems.map(svc => {
        let connected = pricingStructure.find(e => e.sps_service_type === svc.connected_service || e.sps_service_type === svc.service_name);

        if (typeof (connected) != "undefined") {
          const now = new Date();
          svc.date = moment(svc.time).format("MM/DD/YYYY");
          svc.time = moment(svc.time).format("MM/DD/YYYY hh:mm a");
          // svc.date = dateFns.format(Date.parse(svc.time), "MM/DD/YYYY");
          // svc.time = dateFns.format(Date.parse(svc.time), "MM/DD/YYYY hh:mm a");

          svc.desc = svc.time;
          svc.invoice_num = 1001;
          svc.invoice_date = moment(new Date()).format("MM/DD/YYYY");
          svc.due_date = new Date(now.setMonth(now.getMonth() + 1));
          svc.contract_type = connected.sps_contract_type;
          svc.service_cat = connected.sps_service_type;
          svc.quantity = 1;
          svc.hours = svc.hours === null ? 1 : svc.hours;
          svc.people = svc.people === null ? 1 : svc.people;

          if (connected.sps_service_option_trigger === 'tm') {
            svc.price = connected.sps_price;
            //It is time and materials which means multiply hours with price
            if (svc.service_cat.startsWith('PUSH')) {
              svc.total = svc.hours * svc.people * connected.sps_price;
            }
            else {
              //Deicing
              svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price)
            }
          }

          else if (connected.sps_service_option_trigger === 'po') {
            svc.price = connected.sps_price;
            svc.total = connected.sps_price;
          }

          else if (connected.sps_service_option_trigger === 'pppa') {
            svc.price = connected.sps_price;
            if (svc.service_cat.startsWith('PUSH')) {
              //Now here price will be on threshold

              svc.snow_inch = reportService.getSnowDataOptions(svc.options)
              //Lets see in which category does snow fall in

              let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
              if (typeof connectedSnow != 'undefined') {
                //Now due to hybrid contracts we also have to check if selected pricing should come from equipment rate
                if (svc.equipment_id > 0) {
                  let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                  if (connectedTandMRow != undefined) {
                    //Since this is t and m, we need to apply other stuff
                    svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                  }
                } else {
                  svc.total = connectedSnow.sps_price
                }
              }
              else {
                //Here we need to check if it falls under 12+ or time and materials
                connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                  svc.total = svc.snow_inch * connectedSnow.sps_price;
                } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                  //Tandm rates will apply
                  //Now here we have to check the equipment used and multiply its price
                  let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                  svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                }
                else {
                  //If user has selected time and material rates will apply, we need to code that
                  svc.total = 0
                }
              }
            }
            else {
              //Deicing
              svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price)
            }
          }

          else if (connected.sps_service_option_trigger === 'pepipa') {

            if (svc.service_cat.startsWith('PUSH')) {
              if (connected.sps_service_type === 'PUSH_LOTS') {
                // svc.total = connected.sps_price;
                svc.snow_inch = perEventCalculation.snow;

                if (!eventPushLotsFirstrow) {
                  let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
                  if (typeof connectedSnow != 'undefined') {
                    svc.total = connectedSnow.sps_price
                  }
                  else {
                    connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                    if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                      svc.total = svc.snow_inch * connectedSnow.sps_price;
                    } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                      //Tandm rates will apply
                      //Now here we have to check the equipment used and multiply its price
                      let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                      svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                    }
                    else {
                      //If user has selected time and material rates will apply, we need to code that
                      svc.total = 0
                    }
                  }
                  eventPushLotsFirstrow = true;
                }

              }
              if (connected.sps_service_type === 'PUSH_WALKS') {
                // svc.total = connected.sps_price;
                svc.snow_inch = perEventCalculation.snow;
                if (!eventPushWalksFirstrow) {
                  let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
                  if (typeof connectedSnow != 'undefined')
                    svc.total = connectedSnow.sps_price
                  else {
                    connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                    if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                      svc.total = svc.snow_inch * connectedSnow.sps_price;
                    } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                      //Tandm rates will apply
                      //Now here we have to check the equipment used and multiply its price
                      let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                      svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                    }
                    else {
                      //If user has selected time and material rates will apply, we need to code that
                      svc.total = 0
                    }
                  }
                  eventPushWalksFirstrow = true;
                }

              }
              if (connected.sps_service_type === 'PUSH_PUBLIC_WALKS') {
                // svc.total = connected.sps_price;
                svc.snow_inch = perEventCalculation.snow;
                if (!eventPushPublicWalksFirstrow) {
                  let connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && svc.snow_inch >= obj.sps_snow_trigger_start && svc.snow_inch < obj.sps_snow_trigger_end)
                  if (typeof connectedSnow != 'undefined')
                    svc.total = connectedSnow.sps_price
                  else {
                    connectedSnow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && (obj.sps_snow_trigger_start === 122 || obj.sps_snow_trigger_start === 123))
                    if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 122) {
                      svc.total = svc.snow_inch * connectedSnow.sps_price;
                    } else if (typeof connectedSnow != 'undefined' && connectedSnow.sps_snow_trigger_start == 123) {
                      //Tandm rates will apply
                      //Now here we have to check the equipment used and multiply its price
                      let connectedTandMRow = pricingStructure.find(obj => obj.sps_service_type === svc.connected_service && obj.sps_equipmentservice_id === svc.equipment_id);
                      svc.total = svc.people * svc.hours * connectedTandMRow.sps_price;
                    }
                    else {
                      //If user has selected time and material rates will apply, we need to code that
                      svc.total = 0
                    }
                  }
                  eventPushPublicWalksFirstrow = true;
                }
              }
            }
            else {
              //Deicing
              svc.price = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
              svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);

              if (connected.sps_service_type === 'DEICE_LOTS') {
                perEventCalculation.deiceLotPrice += reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
              }
              if (connected.sps_service_type === 'DEICE_WALKS') {
                perEventCalculation.deiceWalkPrice += reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
              }
              if (connected.sps_service_type === 'DEICE_PUBLIC_WALKS') {
                perEventCalculation.deicePublicWalkPrice += reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price);
              }
            }
          }

          else if (connected.sps_service_option_trigger === 'su') {
            // svc.total = connected.sps_price;
            svc.total = 0;
          }

          else if (connected.sps_service_option_trigger === 'sh') {
            if (svc.service_cat.startsWith('DEICE')) {
              svc.total = reportService.getDeicePrice(svc.options, connected.sps_price, connected.sps_partial_price)
            } else {
              svc.total = 0;
            }
          }
          return svc;
        }
      });
      rawData = rawData.filter(s => s != null);

      //Now that we have data ready, we have to do insertion
      let obj = {
        sps_vendor_id: vendorId,
        sps_contractor_id: cid,
        sps_report_status: 1,
        sre_data_json: rawData
      };
      let insertedRecord = await mysql.insertObj('sitefotos_postdated_services', obj)
      let insertedId = insertedRecord.insertId;
      let queryString = hashids.encode([insertedId, vendorId, moment().unix()]);
      let updatedObj = {
        sps_querystring: queryString
      };
      let updatedRecord = await mysql.updateObj('sitefotos_postdated_services', updatedObj, ['sre_id'], [insertedId]);
      // //This also needs to go in email
      const vars = {
        url: `${baseURL}/node/reports/post-dated-services?q=${queryString}`,
        firstName: contractorName,
        companyname: companyName,
        vendorcompanyurl: contractorLogo
      }
      // const html = Mustache.render(fs.readFileSync('./static/postdatedservices-email.html', 'utf8'), vars);
      //
      // let msg = {
      //   'html': html,
      //   'subject': 'Contractor Work Summary from '+ senderVendorDetails[0].companyname, //  {{sender company name}}
      //   'from_email': '<EMAIL>',
      //   'from_name': senderVendorDetails[0].companyname, // {{sender company name}}
      //   'to' :[
      //     {
      //       'email': contractorEmail,
      //       'name': contractorName,
      //       'type': 'to'
      //     }
      //   ],
      //   'headers':
      //       {
      //         'Reply-To': '<EMAIL>'
      //       },
      //   'important':false,
      //   'track_opens' : true,
      //   'track_clicks' :true,
      //   'auto_text' : null,
      //   'auto_html' :null,
      //   'inline_css' : null,
      //   'url_strip_qs' :null,
      //   'preserve_recipients': null,
      //   'view_content_link' :null,
      //   'bcc_address' : null,
      //   'tracking_domain' : null,
      //   'signing_domain' : null,
      //   'return_path_domain': null,
      //   'merge':true,
      //   'merge_language' : 'mailchimp',
      //   'metadata' : {
      //     'website' : 'www.sitefotos.com'
      //   }
      // }
      //TODO: There are two methods for batch of batch contractor, get rid of the one that is not being used
      // let result = await mailchimp.messages.send({message:msg})
      // console.log("postdatedserviceemailbatch",result)
    }
    res.send({ message: 1 });
  } catch (error) {
    next(error)
  }
}

const postDatedServices = async (req, res, next) => {
  try {
    let queryString = req.query.q;
    //check if a-z,0-9,A-Z
    if (!/^[a-zA-Z0-9]+$/.test(queryString)) {
     //send 401
     res.status(401).send("Invalid query string");
     return;
    }
    //Lets find vendor related details to load the store
    let postDatedRow = await awaitSafeQuery(`SELECT * FROM sitefotos_postdated_services sps where sps.sps_querystring=?`, [queryString]);
    if (postDatedRow.length > 0) {
      let vendorId = postDatedRow[0].sps_vendor_id;

      let vendorDetails = await awaitSafeQuery
        (`
        select * from maptile_vendors mv where mv.vendor_id =?
      `, [vendorId]);
      let accessCode = vendorDetails.length > 0 ? vendorDetails[0].vendor_access_code : "";
      //generate a random session id which is uuid v4
      const sessionId = uuid.v4();
      const data = {
        myvtem: vendorId,
        myvid: accessCode,
        sessionId: sessionId,
        // globalServerGroups: groups,
        // myicons: JSON.parse(icons),
        // mycompanylogo: vendorData[0].vendor_company_logo,
        // mycompanyname: vendorData[0].vendor_company_name,
        // myfname: vendorData[0].vendor_fname,
        // mylname: vendorData[0].vendor_lname,
        // userpermissions: JSON.parse(permissions[0].supr_permission_details),
        // globalExternalFlag: true,
        // globalExternalProvider: provider
      }
      res.render('reports/pricingreport', { data: data, query_string: queryString, base_url: baseURL });
    } else {
      res.send("");
    }
  } catch (e) {
    next(e);
  }
}

const postFormToTableToGoogleSheets = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      res.status(401).send(err.message);
      return
    }

    let data = req.body.data;
    let sheetName = req.body.sheetName;
    let sheetUrl = await reportService.postFormToTableReportToGoogleSheets(vendorId, data, sheetName)

    res.send(sheetUrl);
  } catch (e) {
    next(e);
  }
}

const getSitesWithNoActivity = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      res.status(401).send(err.message);
      return
    }

    let startUnix = req.query.start;
    let endUnix = req.query.end;

    let reports = await reportService.fetchSitesWithNoServicesInGivenTimePeriod(vendorId, startUnix, endUnix);
    res.send(reports);
  } catch (e) {
    next(e);
  }
}

const workOrderVerificationReport = async (req, res, next) => {
  try {
    let { vendorId } = await login(req.cookies.JWT);
    if (!vendorId) {
      res.status(401).send(err.message);
      return
    }

    if (req.body) {
      const { page = 1, size = 100, sort = [], start, end } = req.body;
      let reports = await reportService.fetchWorkordersReport(vendorId, start, end, page, size, sort);
      res.send(reports)
    }
  } catch (e) {
    next(e);
  }
}

const exportReportAsGoogleSheet = async (req, res, next) => {
  try {
    const { vendorId, internalUid } = await login(req.cookies.JWT);
    let sheetName = req.body.sheet_name;
    let data = req.body.data;
    let sheetUrl = await reportService.postToGoogle(
      'https://script.google.com/macros/s/AKfycbwRCAg5YJmwvFdAOwlDNPZRv8lXHN5iI46qzRvkMALn-A7mWRnigZPHAPbgS3si9-kLIQ/exec',
      vendorId,
      sheetName,
      data
    );
    res.send(sheetUrl);
  } catch (error) {
    next(error);
  }
}
async function getWellsFargoWinterServicesTable(req, res, next) {
  try {
    const { vendorId, internalUid, isClientViewEnabled, isHybrid } = await login(req.cookies.JWT);
    let { startdate: start, enddate: end } = req.query;
    let mapping = await getSnowVendorMapping();
    let uniqueVendors = [...new Set(mapping.map(item => item.vendor_id))];
   
    let sites = [];
    if(isClientViewEnabled) {
      let buildings = await awaitSafeQuery(`select sscm_site_id from sitefotos_site_client_mapping_extended_ungrouped where scs_client_vendor_id = ?`, [vendorId]);
      sites = buildings.map(b => b.sscm_site_id);
    }
    

    const allForms = [];
    const allFields = new Set();


    const startTimestamp = parseInt(start);
    const endTimestamp = parseInt(end);
    const oneDay = 86400;

    let currentDayStart = startTimestamp;

   

    // Prepare base query
    const baseQuery = `SELECT
           sfs_id AS submittedid,
           sfs_checkin AS checkin,
           sfs_checkout AS checkout,
           sfs_created AS Date,
           sfs_building_id AS buildingid,
           sfs_uploader_email AS Email,
           sfs_form_id AS formid,
           sfs_vendor_id AS vendorId,
           sfs_form_name AS Form,
           mb_nickname AS Property,
           (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) AS Zone,
           sfs_form_data_full
         FROM sitefotos_forms_submitted
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id LEFT OUTER JOIN sitefotos_forms on sf_id = sfs_form_id
         WHERE sfs_building_id IN (${sites.join(',')})
           AND sf_form_name = 'Wells Fargo Winter Form' and sf_form_locked = 1 and sf_active = 1
           AND sfs_created >= ?
           AND sfs_created < ? AND sfs_vendor_id IN (${uniqueVendors.join(',')})`;


    while (currentDayStart < endTimestamp) {
      const currentDayEnd = currentDayStart + oneDay;
      let lastSfsId = 0;
      const batchSize = 50;


      while (true) {
        const query = `${baseQuery} AND sfs_id > ? ORDER BY sfs_id ASC`;
        const queryParams = [
          currentDayStart,
          currentDayEnd,
          lastSfsId,
        ];

        const results = await awaitQuery(query, queryParams);

        if (results.length === 0) break;

        for (const result of results) {
          const formData = JSON.parse(result.sfs_form_data_full);
          const jsonElements = JSONPath.query(formData, '$..elements[*]');

          const obj = {
            Date: result.Date,
            Building: result.Property,
            'Form Name': result.Form,
            vendorId: result.vendorId,
            buildingId: result.buildingid,
            "Service Provider": "",
            "Region": "",
            "State": "",
            "Property Manager": "",
            "Regional Manager": "",
            "Percipitation": "",
            "Report Link": "https://www.sitefotos.com/vpics/printformclient?fid=" + result.submittedid

           
          };

          
            obj.CheckIn = result.checkin;
            obj.CheckOut = result.checkout;
          

          for (const element of jsonElements) {
            if (element.type && element.title) {

              while (obj[element.title]) {
                element.title += ' ';
              }

              switch (element.type) {
                case 'geo':
                  obj[element.title] = `${element.lat},${element.lng}`;
                  break;
                case 'signaturepad':
                  const signatureUrls = Array.isArray(element.value)
                    ? element.value.map((v) => v.lrImageURL)
                    : element.value?.lrImageURL
                    ? [element.value.lrImageURL]
                    : [];
                  obj['Signature'] = signatureUrls.length > 0 ? signatureUrls : 'No Signature';
                  break;
                case 'file':
                  const fileUrls = Array.isArray(element.value)
                    ? element.value.map((v) => v.lrImageURL)
                    : [];
                  obj[element.title] = fileUrls.length > 0 ? fileUrls : 'No Photos';
                  break;
                case 'dropdownmultiple':
                  obj[element.title] = element.value.join(',');
                  break;
                case 'service':
                  obj[element.title] = processServiceType(element);
                  break;
                case 'material':
                  const usage = element.value ?? element.quantity;
                  const unit = element.unit;
                  obj[element.title] = unit && usage ? `${parseFloat(usage)} ${unit}` : '';
                  break;
                case 'issues':
                  obj[element.title] = element.value.map((item) => item.title).join(', ');
                  break;
                default:
                  const value = Array.isArray(element.value) ? element.value.join(',') : element.value;
                  if (value) {
                    const title = element.title.replace(/\./g, '');
                    obj[title] = value;
                  }
                  break;
              }
            }
          }


          Object.keys(obj).forEach((key) => allFields.add(key));
          allForms.push(obj);
        }


        lastSfsId = results[results.length - 1].submittedid;
      }


      currentDayStart = currentDayEnd;
    }


    allForms.forEach((form) => {
      allFields.forEach((field) => {
        if (!form.hasOwnProperty(field)) {
          form[field] = '';
        }
      });
    });

    res.json({forms: allForms, vendors: mapping});
  } catch (ex) {
    next(ex);
  }
}

async function uspsSitesReport(req, res, next) {

  try {
    const { vendorId, internalUid, isClientViewEnabled, isHybrid } = await login(req.cookies.JWT);
     if (!vendorId) {
      return res.status(401).json({ message: 'Invalid vendor' });
    }
    const { start, end } = req.query;
  
    // Validate start timestamp
    const startTimestamp = parseInt(start);
    const endTimestamp = parseInt(end);
    if (isNaN(startTimestamp) || startTimestamp <= 0 || isNaN(endTimestamp) || endTimestamp <= 0 || startTimestamp >= endTimestamp) {
      return res.status(400).json({ message: 'Invalid Date Range' });
    }
    const result = await reportService.getUSPSSitesReport(internalUid, startTimestamp, endTimestamp);
    res.status(200).json(result);
  } catch (error) {
    console.error("Error generating report:", error);
    if (error.message) {
      return res.status(400).json({ message: error.message });
    }
    res.status(400).json({ message: 'Internal Server Error' });
  }
}

async function form2table(req, res, next) {
  try {
    const { vendorId, internalUid, isClientViewEnabled, isHybrid } = await login(req.cookies.JWT);
    let { formid, startdate: start, enddate: end } = req.query;
    const vendorCheckDisabled = isHybrid || isClientViewEnabled;
    let multiple = false;
    let sites = [];
    if(isClientViewEnabled) {
      let buildings = await awaitSafeQuery(`select sscm_site_id from sitefotos_site_client_mapping_extended_ungrouped where scs_client_vendor_id = ?`, [vendorId]);
      sites = buildings.map(b => b.sscm_site_id);
    }
    if (formid.includes(',')) {
      formid = formid.split(',').map(Number).join(',');
      multiple = true;
    } else {
      formid = parseInt(formid);
    }

    const allForms = [];
    const allFields = new Set();


    const startTimestamp = parseInt(start);
    const endTimestamp = parseInt(end);
    const oneDay = 86400;

    let currentDayStart = startTimestamp;

    // Fetch check-in requirement
    const checkinQuery = multiple
      ? `SELECT sf_form_checkout FROM sitefotos_forms WHERE sf_id IN (${formid})`
      : `SELECT sf_form_checkout FROM sitefotos_forms WHERE sf_id = ?`;

    const checkinResults = await awaitQuery(checkinQuery, multiple ? [] : [formid]);
    const checkin = checkinResults[0]?.sf_form_checkout ?? 0;

    // Prepare base query
    const baseQuery = multiple
      ? `SELECT
           sfs_id AS submittedid,
           sfs_checkin AS checkin,
           sfs_checkout AS checkout,
           sfs_created AS Date,
           sfs_building_id AS buildingid,
           sfs_uploader_email AS Email,
           sfs_form_id AS formid,
           sfs_vendor_id AS vendorId,
           sfs_form_name AS Form,
           mb_nickname AS Property,
           (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) AS Zone,
           sfs_form_data_full
         FROM sitefotos_forms_submitted
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
         WHERE ${vendorCheckDisabled ? '1 = ?' : 'sfs_vendor_id = ?'}
           AND sfs_form_id IN (${formid})
           AND sfs_created >= ?
           AND sfs_created < ?`
      : `SELECT
           sfs_id AS submittedid,
           sfs_checkin AS checkin,
           sfs_checkout AS checkout,
           sfs_created AS Date,
           sfs_building_id AS buildingid,
           sfs_uploader_email AS Email,
           sfs_form_id AS formid,
           sfs_vendor_id AS vendorId,
           sfs_form_name AS Form,
           mb_nickname AS Property,
           (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) AS Zone,
           sfs_form_data_full
         FROM sitefotos_forms_submitted
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
         WHERE ${vendorCheckDisabled ? '1 = ?' : 'sfs_vendor_id = ?'} ${sites.length > 0 ? `AND sfs_building_id IN (${sites.join(',')})` : ''}
           AND sfs_form_id = ?
           AND sfs_created >= ?
           AND sfs_created < ?`;


    while (currentDayStart < endTimestamp) {
      const currentDayEnd = currentDayStart + oneDay;
      let lastSfsId = 0;
      const batchSize = 50;


      while (true) {
        const query = `${baseQuery} AND sfs_id > ? ORDER BY sfs_id ASC`;
        const queryParams = [
          vendorCheckDisabled ? 1 : vendorId,
          ...(multiple ? [] : [formid]),
          currentDayStart,
          currentDayEnd,
          lastSfsId,
        ];

        const results = await awaitQuery(query, queryParams);

        if (results.length === 0) break;

        for (const result of results) {
          const formData = JSON.parse(result.sfs_form_data_full);
          const jsonElements = JSONPath.query(formData, '$..elements[*]');

          const obj = {
            Date: result.Date,
            Building: result.Property,
            'Form Name': result.Form,
            Email: result.Email,
            vendorId: result.vendorId,
            'Submission ID': result.submittedid,
          };

          if (checkin === 1) {
            obj.CheckIn = result.checkin;
            obj.CheckOut = result.checkout;
          }

          for (const element of jsonElements) {
            if (element.type && element.title) {

              while (obj[element.title]) {
                element.title += ' ';
              }

              switch (element.type) {
                case 'geo':
                  obj[element.title] = `${element.lat},${element.lng}`;
                  break;
                case 'signaturepad':
                  const signatureUrls = Array.isArray(element.value)
                    ? element.value.map((v) => v.lrImageURL)
                    : element.value?.lrImageURL
                    ? [element.value.lrImageURL]
                    : [];
                  obj['Signature'] = signatureUrls.length > 0 ? signatureUrls : 'No Signature';
                  break;
                case 'file':
                  const fileUrls = Array.isArray(element.value)
                    ? element.value.map((v) => v.lrImageURL)
                    : [];
                  obj[element.title] = fileUrls.length > 0 ? fileUrls : 'No Photos';
                  break;
                case 'dropdownmultiple':
                  obj[element.title] = element.value ? element.value.join(',') : '';
                  break;
                case 'service':
                  obj[element.title] = processServiceType(element);
                  break;
                case 'material':
                  const usage = element.value ?? element.quantity;
                  const unit = element.unit;
                  obj[element.title] = unit && usage ? `${parseFloat(usage)} ${unit}` : '';
                  break;
                case 'issues':
                  obj[element.title] = element.value ? element.value.map((item) => item.title).join(', ') : '';
                  break;
                default:
                  const value = Array.isArray(element.value) ? element.value.join(',') : element.value;
                  if (value) {
                    const title = element.title.replace(/\./g, '');
                    obj[title] = value;
                  }
                  break;
              }
            }
          }


          Object.keys(obj).forEach((key) => allFields.add(key));
          allForms.push(obj);
        }


        lastSfsId = results[results.length - 1].submittedid;
      }


      currentDayStart = currentDayEnd;
    }


    allForms.forEach((form) => {
      allFields.forEach((field) => {
        if (!form.hasOwnProperty(field)) {
          form[field] = '';
        }
      });
    });

    res.json(allForms);
  } catch (ex) {
    next(ex);
  }
}

async function exportSafeBySixToGoogleSheet (req, res, next){
  try {
    const logo_1 = req.body.logo_1;
    const logo_2 = req.body.logo_2;
    const sheet_name = req.body.sheet_name;
    const data = req.body.data;
    const gSheetReq = await fetch('https://script.google.com/macros/s/AKfycbw8REyMjyXuUogksdcfowas260xs6YhfmiwuFfGCf8WuuKgONT04TLRP4aFuBWycC1U8Q/exec',{
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({
        "logos": {
          "logo_1": logo_1,
          "logo_2": logo_2
        },
        "sheet_name": sheet_name,
        "data": data
      })
    });
    const resp = await gSheetReq.text();
    res.send(resp);
  }
  catch (ex) {
    next(ex);
  }
}

async function safeBySix(req, res, next) {
  try {
    const { vendorId, internalUid, isClientViewEnabled, isHybrid } = await login(req.cookies.JWT);
    let { formid, startdate: start, enddate: end } = req.query;
    const vendorCheckDisabled = isHybrid || isClientViewEnabled;
    let multiple = false;


    if (formid.includes(',')) {
      formid = formid.split(',').map(Number).join(',');
      multiple = true;
    } else {
      formid = parseInt(formid);
    }

    const allForms = [];
    const allFields = new Set();


    const startTimestamp = parseInt(start);
    const endTimestamp = parseInt(end);
    const oneDay = 86400;

    let currentDayStart = startTimestamp;

    // Fetch check-in requirement
    const checkinQuery = multiple
      ? `SELECT sf_form_checkout FROM sitefotos_forms WHERE sf_id IN (${formid})`
      : `SELECT sf_form_checkout FROM sitefotos_forms WHERE sf_id = ?`;

    const checkinResults = await awaitQuery(checkinQuery, multiple ? [] : [formid]);
    const checkin = checkinResults[0]?.sf_form_checkout ?? 0;

    // Prepare base query
    const baseQuery = multiple
      ? `SELECT
           sfs_id AS submittedid,
           sfs_checkin AS checkin,
           sfs_checkout AS checkout,
           sfs_created AS Date,
           sfs_building_id AS buildingid,
           sfs_uploader_email AS Email,
           sfs_form_id AS formid,
           sfs_vendor_id AS vendorId,
           sfs_form_name AS Form,
           mb_nickname AS Property,
           mb_address1 AS Address,
           (select City from maptile_city where CityId = mb_city) as City,
           (select state_abv_name from maptile_state where id = mb_state) as State,
           (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) AS Zone,
           sfs_form_data_full
         FROM sitefotos_forms_submitted
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
         WHERE ${vendorCheckDisabled ? '1 = ?' : 'sfs_vendor_id = ?'}
           AND sfs_form_id IN (${formid})
           AND sfs_created >= ?
           AND sfs_created < ?`
      : `SELECT
           sfs_id AS submittedid,
           sfs_checkin AS checkin,
           sfs_checkout AS checkout,
           sfs_created AS Date,
           sfs_building_id AS buildingid,
           sfs_uploader_email AS Email,
           sfs_form_id AS formid,
           sfs_vendor_id AS vendorId,
           sfs_form_name AS Form,
           mb_nickname AS Property,
           mb_address1 AS Address,
           (select City from maptile_city where CityId = mb_city) as City,
           (select state_abv_name from maptile_state where id = mb_state) as State,
           (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) AS Zone,
           sfs_form_data_full
         FROM sitefotos_forms_submitted
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
         WHERE ${vendorCheckDisabled ? '1 = ?' : 'sfs_vendor_id = ?'}
           AND sfs_form_id = ?
           AND sfs_created >= ?
           AND sfs_created < ?`;


    while (currentDayStart < endTimestamp) {
      const currentDayEnd = currentDayStart + oneDay;
      let lastSfsId = 0;
      const batchSize = 50;


      while (true) {
        const query = `${baseQuery} AND sfs_id > ? ORDER BY sfs_id ASC`;
        const queryParams = [
          vendorCheckDisabled ? 1 : vendorId,
          ...(multiple ? [] : [formid]),
          currentDayStart,
          currentDayEnd,
          lastSfsId,
        ];

        const results = await awaitQuery(query, queryParams);

        if (results.length === 0) break;

        for (const result of results) {
          const formData = JSON.parse(result.sfs_form_data_full);
          const jsonElements = JSONPath.query(formData, '$..elements[*]');

          const obj = {
            'Site Name': result.Property,
            'Site Address': result.Address,
            City: result.City,
            Region: result.State,
            'Form Name': result.Form,
            Date: result.Date,
            Email: result.Email,
            vendorId: result.vendorId,
            'Submission ID': result.submittedid,
          };

          if (checkin === 1) {
            obj.CheckIn = result.checkin;
            obj.CheckOut = result.checkout;
          }

          for (const element of jsonElements) {
            if (element.type && element.title) {

              while (obj[element.title]) {
                element.title += ' ';
              }

              switch (element.type) {
                case 'geo':
                  obj[element.title] = `${element.lat},${element.lng}`;
                  break;
                case 'signaturepad':
                  const signatureUrls = Array.isArray(element.value)
                    ? element.value.map((v) => v.lrImageURL)
                    : element.value?.lrImageURL
                      ? [element.value.lrImageURL]
                      : [];
                  obj['Signature'] = signatureUrls.length > 0 ? signatureUrls : 'No Signature';
                  break;
                case 'file':
                  const fileUrls = Array.isArray(element.value)
                    ? element.value.map((v) => v.lrImageURL)
                    : [];
                  obj[element.title] = fileUrls.length > 0 ? fileUrls : 'No Photos';
                  break;
                case 'dropdownmultiple':
                  obj[element.title] = element.value.join(',');
                  break;
                case 'service':
                  obj[element.title] = processServiceType(element);
                  break;
                case 'material':
                  const usage = element.value ?? element.quantity;
                  const unit = element.unit;
                  obj[element.title] = unit && usage ? `${parseFloat(usage)} ${unit}` : '';
                  break;
                case 'issues':
                  obj[element.title] = element.value.map((item) => item.title).join(', ');
                  break;
                default:
                  const value = Array.isArray(element.value) ? element.value.join(',') : element.value;
                  if (value) {
                    const title = element.title.replace(/\./g, '');
                    obj[title] = value;
                  }
                  break;
              }
            }
          }


          Object.keys(obj).forEach((key) => allFields.add(key));
          allForms.push(obj);
        }


        lastSfsId = results[results.length - 1].submittedid;
      }


      currentDayStart = currentDayEnd;
    }


    allForms.forEach((form) => {
      allFields.forEach((field) => {
        if (!form.hasOwnProperty(field)) {
          form[field] = '';
        }
      });
    });

    res.json(allForms);
  } catch (ex) {
    next(ex);
  }
}

async function form2tableOld(req, res, next) {
  try {
    const { vendorId, internalUid, isClientViewEnabled, isHybrid } = await login(req.cookies.JWT);
    let { formid, startdate: start, enddate: end } = req.query;
    const vendorCheckDisabled = isHybrid || isClientViewEnabled;
    let multiple = false;
    if (formid.includes(',')) {
      formid = formid.split(',').map(Number).join(',');
      multiple = true;
    } else {
      formid = parseInt(formid);
    }

    const batchSize = 50;
    let offset = 0;
    const allForms = [];
    const allFields = new Set();

    const baseQuery = multiple ?
      `SELECT sfs_id AS submittedid, sfs_checkin as checkin, sfs_checkout as checkout, sfs_created AS Date, 
         sfs_building_id AS buildingid, sfs_uploader_email AS Email, sfs_form_id AS formid, sfs_vendor_id as vendorId,
         sfs_form_name AS Form, mb_nickname AS Property, 
         (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) as Zone, 
         sfs_form_data_full 
         FROM sitefotos_forms_submitted  
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
         WHERE sfs_created <= ? AND sfs_created >= ? AND ${vendorCheckDisabled ? '1=?' : 'sfs_vendor_id = ?'} AND sfs_form_id IN (${formid})` :
      `SELECT sfs_id AS submittedid, sfs_checkin as checkin, sfs_checkout as checkout, sfs_created AS Date, 
         sfs_building_id AS buildingid, sfs_uploader_email AS Email, sfs_form_id AS formid, sfs_vendor_id as vendorId,
         sfs_form_name AS Form, mb_nickname AS Property, 
         (SELECT sgz_geo_zone FROM sitefotos_geofence_zones WHERE sgz_zone_id = mb_zone_id) as Zone, 
         sfs_form_data_full  
         FROM sitefotos_forms_submitted  
         LEFT OUTER JOIN maptile_building ON mb_id = sfs_building_id
         WHERE sfs_created <= ? AND sfs_created >= ? AND ${vendorCheckDisabled ? '1=?' : 'sfs_vendor_id = ?'} AND sfs_form_id = ?`;

    const checkinQuery = multiple ?
      `SELECT sf_form_checkout FROM sitefotos_forms WHERE sf_id IN (${formid})` :
      `SELECT sf_form_checkout FROM sitefotos_forms WHERE sf_id = ?`;

    const checkinResults = await awaitQuery(checkinQuery, multiple ? [] : [formid]);
    const checkin = checkinResults[0]?.sf_form_checkout ?? 0;

    while (true) {
      const query = `${baseQuery} LIMIT ${batchSize} OFFSET ${offset}`;
      const results = await awaitQuery(query, [end, start, vendorCheckDisabled ? 1 : vendorId, ...(multiple ? [] : [formid])]);

      if (results.length === 0) break;

      for (const result of results) {
        const formData = JSON.parse(result.sfs_form_data_full);
        const json = JSONPath.query(formData, '$..elements[*]');

        const obj = {
          Date: result.Date,
          Building: result.Property,
          "Form Name": result.Form,
          "Email": result.Email,
          "vendorId": result.vendorId,
          "Submission ID": result.submittedid,
        };

        if (checkin === 1) {
          obj.CheckIn = result.checkin;
          obj.CheckOut = result.checkout;
        }

        for (const element of json) {
          if (element.type) {
            while (obj[element.title]) {
              element.title += ' ';
            }

            switch (element.type) {
              case 'geo':
                obj[element.title] = `${element.lat},${element.lng}`;
                break;
              case 'signaturepad':
                const signatureUrls = Array.isArray(element.value)
                  ? element.value.map(v => v.lrImageURL)
                  : (element.value?.lrImageURL ? [element.value.lrImageURL] : []);
                obj['Signature'] = signatureUrls.length > 0 ? signatureUrls : 'No Signature';
                break;
              case 'file':
                const fileUrls = Array.isArray(element.value)
                  ? element.value.map(v => v.lrImageURL)
                  : [];
                obj[element.title] = fileUrls.length > 0 ? fileUrls : 'No Photos';
                break;
              case 'dropdownmultiple':
                obj[element.title] = element.value.join(',');
                break;
              case 'service':
                obj[element.title] = processServiceType(element);
                break;
              case 'material':
                const usage = element.value ?? element.quantity;
                const unit = element.unit;
                obj[element.title] = unit && usage ? `${parseFloat(usage)} ${unit}.` : '';
                break;
              case 'issues':
                obj[element.title] = element.value.map(item => item.title).join(', ');
                break;
              default:
                if (element.value && element.title) {
                  const value = Array.isArray(element.value) ? element.value.join(',') : element.value;
                  if (value) {
                    const title = element.title.replace(/\./g, '');
                    obj[title] = value;
                  }
                }
                break;
            }
          }
        }

        Object.keys(obj).forEach(key => allFields.add(key));
        allForms.push(obj);
      }

      offset += batchSize;
    }


    allForms.forEach(form => {
      allFields.forEach(field => {
        if (!form.hasOwnProperty(field)) {
          form[field] = '';
        }
      });
    });

    res.json(allForms);
  } catch (ex) {
    next(ex);
  }
}

function processServiceType(element) {
  let options = element.Options;
 
  if (!Array.isArray(options)) {
    if (typeof options === 'string') {
      options = options.split(',');
    } else {
      options = options ? [options] : [];
    }
  }

  let value = '';
  const serviceType = element.serviceType ?? element.Completed ?? element.People ?? (element.StartTime ? 'AsTimer' : null);

  if (serviceType === 'AsTask') {
    const completed = element.Completed;
    value = completed ? `Completed.${options.join(',')}` : "Not Completed.";
  } else if (serviceType === 'AsDetail') {
    const people = element.People ?? element.people;
    const hours = element.Hour ?? element.hours;
    if (people && hours) {
      value = `${people} People, ${hours} Hour(s). ${options.join(',')}`;
    }
  } else if (serviceType === 'AsTimer') {
    const starttime = element.StartTime;
    const stoptime = element.StopTime;
    if (starttime && stoptime) {
      const timespent = Math.floor((stoptime - starttime) / 60);
      value = `${timespent} Minutes. ${options.join(',')}`;
    }
  }
  return value;
}


module.exports = {
  getFormToTable,
  getFormsByType,
  getPricingReport,
  getPricingReportContractor,
  getPricingReportClient,
  getFormReportExtended,
  getServicesSnowReport,
  getClientContractTypesBySites,
  checkinsWithoutCheckout,
  getContractorDetails,
  getReportEmail,
  updateReportEmail,
  getServiceDetails,
  getPerOccuranceJobCostReport,
  getWellsFargoDPREvents,
  reportAllContractors,
  sendBatchReportInEmail,
  siteSessionsServices,
  siteSessions,
  getWeeklyTimeSheet,
  getServiceTrackerBySites,
  getServiceTrackerBySitesClient,
  postDatedServices,
  getServiceRangeData,
  getMaterialRangeData,
  getTrueNorthReport1,
  postFormToTableToGoogleSheets,
  getSitesWithNoActivity,
  workOrderVerificationReport,
  exportReportAsGoogleSheet,
  photoReportLocalTime,
  getSumTestReport,
  getWinterServiceTrackerWellsFargo,
  getWellsFargoWeeklyLandscapeReport,
  getWellsFargoSnowStormReport,
  getWellsFargoSnowStormReport2,
  getSnowStorms,
  getStormsInTimeRangeProvider,
  getStormsInTimeRange,
  getCameras,
  getWellsFargoPieReport,
  getWellsFargoPieReportRollup,
  getIssuesReport,
  getWellsFargoSnowStormChartReport,
  getVerizonLandscapingData,
  getVerizonSpringCleanupData,
  getVerizonJanitorialData,
  getVerizonHVACData,
  getWellsFargoDPREventData,
  form2table,
  safeBySix,
  exportSafeBySixToGoogleSheet,
  getVerizonLandscapeChartData,
 getVerizonJanitorialChartDataTime,
 getVerizonTabulatedLandScapeData,
 getVerizonTabulatedJanitorialData,
 getServiceMonitoringRequest,
 getSnowCommandCenterReport,
 getWellsFargoWinterServicesTable,
 getChaseDashboard,
 uspsSitesReport
}
